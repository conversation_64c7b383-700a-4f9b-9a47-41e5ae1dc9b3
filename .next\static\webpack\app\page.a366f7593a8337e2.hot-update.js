/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CDiscoverMore.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CPopularDestinations.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CSearchForm.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CDiscoverMore.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CPopularDestinations.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CSearchForm.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/DiscoverMore.tsx */ \"(app-pages-browser)/./components/DiscoverMore.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/Header.tsx */ \"(app-pages-browser)/./components/Header.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/PopularDestinations.tsx */ \"(app-pages-browser)/./components/PopularDestinations.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./components/SearchForm.tsx */ \"(app-pages-browser)/./components/SearchForm.tsx\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz1DJTNBJTVDVXNlcnMlNUN1c2VyJTVDRG93bmxvYWRzJTVDQ09QWSU1Q2NvbXBvbmVudHMlNUNEaXNjb3Zlck1vcmUudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0Rvd25sb2FkcyU1Q0NPUFklNUNjb21wb25lbnRzJTVDSGVhZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q3VzZXIlNUNEb3dubG9hZHMlNUNDT1BZJTVDY29tcG9uZW50cyU1Q1BvcHVsYXJEZXN0aW5hdGlvbnMudHN4Jm1vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDdXNlciU1Q0Rvd25sb2FkcyU1Q0NPUFklNUNjb21wb25lbnRzJTVDU2VhcmNoRm9ybS50c3gmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUN1c2VyJTVDRG93bmxvYWRzJTVDQ09QWSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNkaXN0JTVDY2xpZW50JTVDbGluay5qcyZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFtRztBQUNuRyx3S0FBNkY7QUFDN0Ysa01BQTBHO0FBQzFHLHNMQUFpRztBQUNqRyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvPzZhMDAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERvd25sb2Fkc1xcXFxDT1BZXFxcXGNvbXBvbmVudHNcXFxcRGlzY292ZXJNb3JlLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEb3dubG9hZHNcXFxcQ09QWVxcXFxjb21wb25lbnRzXFxcXEhlYWRlci50c3hcIik7XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHVzZXJcXFxcRG93bmxvYWRzXFxcXENPUFlcXFxcY29tcG9uZW50c1xcXFxQb3B1bGFyRGVzdGluYXRpb25zLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcdXNlclxcXFxEb3dubG9hZHNcXFxcQ09QWVxcXFxjb21wb25lbnRzXFxcXFNlYXJjaEZvcm0udHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFx1c2VyXFxcXERvd25sb2Fkc1xcXFxDT1BZXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CDiscoverMore.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CHeader.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CPopularDestinations.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Ccomponents%5CSearchForm.tsx&modules=C%3A%5CUsers%5Cuser%5CDownloads%5CCOPY%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/SearchForm.tsx":
/*!***********************************!*\
  !*** ./components/SearchForm.tsx ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import SearchForm from '@/components/SearchForm'
import { Plane, Hotel, Car, MapPin, Star, Users, Shield, Clock } from 'lucide-react'
import Image from 'next/image'

export default function HomePage() {
  return (
    <>
      <Header />
      
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-primary-600 to-secondary-600 text-white py-20">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative container mx-auto px-4">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-6xl font-bold arabic-display mb-4">
                اكتشف العالم معنا
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto">
                احجز رحلاتك وفنادقك وسياراتك بأفضل الأسعار واستمتع بتجربة سفر لا تُنسى
              </p>
            </div>
            
            <SearchForm />
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                لماذا تختار رحلاتي؟
              </h2>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                نوفر لك أفضل الخدمات السياحية بأسعار تنافسية وجودة عالية
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center p-6">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Shield className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">حجز آمن</h3>
                <p className="text-gray-600">
                  نضمن لك حجز آمن ومحمي بأحدث تقنيات الأمان
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-secondary-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="h-8 w-8 text-secondary-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">أفضل الأسعار</h3>
                <p className="text-gray-600">
                  نقارن الأسعار من مئات المواقع لنوفر لك أفضل العروض
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-accent-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Clock className="h-8 w-8 text-accent-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">دعم 24/7</h3>
                <p className="text-gray-600">
                  فريق الدعم متاح على مدار الساعة لمساعدتك
                </p>
              </div>

              <div className="text-center p-6">
                <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-xl font-semibold mb-2">خبرة واسعة</h3>
                <p className="text-gray-600">
                  أكثر من 10 سنوات من الخبرة في مجال السياحة والسفر
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                خدماتنا
              </h2>
              <p className="text-gray-600 text-lg">
                نوفر لك جميع احتياجاتك السياحية في مكان واحد
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Flight Booking */}
              <div className="card hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-primary-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Plane className="h-10 w-10 text-primary-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">حجز الطيران</h3>
                  <p className="text-gray-600 mb-6">
                    احجز تذاكر الطيران لجميع الوجهات العالمية بأفضل الأسعار
                  </p>
                  <ul className="text-right space-y-2 mb-6">
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-primary-600 rounded-full"></span>
                      <span>رحلات داخلية وخارجية</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-primary-600 rounded-full"></span>
                      <span>جميع شركات الطيران</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-primary-600 rounded-full"></span>
                      <span>إلغاء مجاني</span>
                    </li>
                  </ul>
                  <button className="btn-primary w-full">احجز الآن</button>
                </div>
              </div>

              {/* Hotel Booking */}
              <div className="card hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-secondary-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Hotel className="h-10 w-10 text-secondary-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">حجز الفنادق</h3>
                  <p className="text-gray-600 mb-6">
                    اختر من بين آلاف الفنادق حول العالم بأسعار مميزة
                  </p>
                  <ul className="text-right space-y-2 mb-6">
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-secondary-600 rounded-full"></span>
                      <span>فنادق 3-5 نجوم</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-secondary-600 rounded-full"></span>
                      <span>إفطار مجاني</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-secondary-600 rounded-full"></span>
                      <span>إلغاء مرن</span>
                    </li>
                  </ul>
                  <button className="btn-secondary w-full">احجز الآن</button>
                </div>
              </div>

              {/* Car Rental */}
              <div className="card hover:shadow-lg transition-shadow">
                <div className="text-center">
                  <div className="bg-accent-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Car className="h-10 w-10 text-accent-600" />
                  </div>
                  <h3 className="text-2xl font-semibold mb-4">تأجير السيارات</h3>
                  <p className="text-gray-600 mb-6">
                    استأجر سيارة مناسبة لرحلتك من أفضل شركات التأجير
                  </p>
                  <ul className="text-right space-y-2 mb-6">
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-accent-600 rounded-full"></span>
                      <span>جميع أنواع السيارات</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-accent-600 rounded-full"></span>
                      <span>تأمين شامل</span>
                    </li>
                    <li className="flex items-center space-x-2 space-x-reverse">
                      <span className="w-2 h-2 bg-accent-600 rounded-full"></span>
                      <span>استلام من المطار</span>
                    </li>
                  </ul>
                  <button className="btn-outline w-full">احجز الآن</button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Popular Destinations */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                الوجهات الشائعة
              </h2>
              <p className="text-gray-600 text-lg">
                اكتشف أجمل الوجهات السياحية حول العالم
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Destination cards would go here */}
              <div className="relative rounded-lg overflow-hidden shadow-lg group cursor-pointer">
                <div className="h-64 bg-gradient-to-br from-blue-400 to-blue-600"></div>
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">دبي</h3>
                  <p className="text-blue-100">ابتداءً من 1,200 ريال</p>
                </div>
              </div>

              <div className="relative rounded-lg overflow-hidden shadow-lg group cursor-pointer">
                <div className="h-64 bg-gradient-to-br from-green-400 to-green-600"></div>
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">القاهرة</h3>
                  <p className="text-green-100">ابتداءً من 800 ريال</p>
                </div>
              </div>

              <div className="relative rounded-lg overflow-hidden shadow-lg group cursor-pointer">
                <div className="h-64 bg-gradient-to-br from-purple-400 to-purple-600"></div>
                <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors"></div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">إسطنبول</h3>
                  <p className="text-purple-100">ابتداءً من 1,000 ريال</p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

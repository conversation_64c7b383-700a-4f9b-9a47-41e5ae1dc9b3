import Header from '@/components/Header'
import Footer from '@/components/Footer'
import SearchForm from '@/components/SearchForm'
import PopularDestinations from '@/components/PopularDestinations'
import { Plane, Hotel, Car, Star, Users, Shield, Clock } from 'lucide-react'

export default function HomePage() {
  return (
    <>
      <Header />

      <main className="flex-1">
        {/* Cinematic Hero Section */}
        <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
          {/* Video-like Background */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-900/90 via-transparent to-purple-900/90"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"></div>

            {/* Animated Clouds */}
            <div className="absolute top-20 left-0 w-96 h-32 bg-white/5 rounded-full blur-3xl animate-float" style={{animationDuration: '20s'}}></div>
            <div className="absolute top-40 right-0 w-80 h-24 bg-white/3 rounded-full blur-3xl animate-float" style={{animationDuration: '25s', animationDelay: '5s'}}></div>
            <div className="absolute bottom-40 left-1/4 w-64 h-20 bg-white/4 rounded-full blur-3xl animate-float" style={{animationDuration: '30s', animationDelay: '10s'}}></div>

            {/* Flying Planes Animation */}
            <div className="absolute top-1/4 left-0 animate-fly-across">
              <Plane className="h-8 w-8 text-white/30 transform rotate-45" />
            </div>
            <div className="absolute top-1/3 right-0 animate-fly-across-reverse" style={{animationDelay: '8s'}}>
              <Plane className="h-6 w-6 text-white/20 transform -rotate-45" />
            </div>
            <div className="absolute bottom-1/3 left-1/3 animate-fly-diagonal" style={{animationDelay: '15s'}}>
              <Plane className="h-5 w-5 text-white/25 transform rotate-12" />
            </div>
          </div>

          {/* Main Content */}
          <div className="relative container mx-auto px-4 py-20 flex items-center min-h-screen">
            <div className="w-full">
              {/* Cinematic Title */}
              <div className="text-center mb-20">
                {/* Subtitle */}
                <div className="mb-8 animate-fade-in">
                  <span className="inline-block px-6 py-3 bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 rounded-full text-blue-200 text-lg font-medium tracking-wide">
                    ✈️ منصة السفر الذكية
                  </span>
                </div>

                {/* Main Title */}
                <h1 className="text-6xl md:text-8xl lg:text-9xl font-black arabic-title mb-8 animate-slide-up">
                  <span className="block bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent drop-shadow-2xl">
                    رحلاتي
                  </span>
                  <span className="block text-4xl md:text-5xl lg:text-6xl mt-4 bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent font-medium">
                    اكتشف العالم بلا حدود
                  </span>
                </h1>

                {/* Description */}
                <p className="text-xl md:text-2xl lg:text-3xl text-blue-100 max-w-5xl mx-auto leading-relaxed animate-slide-up font-light" style={{animationDelay: '0.3s'}}>
                  انطلق في رحلة استثنائية عبر السماء والأرض
                  <br />
                  <span className="text-lg md:text-xl text-blue-200 mt-2 block">
                    احجز رحلاتك وفنادقك وسياراتك بأفضل الأسعار واستمتع بتجربة سفر لا تُنسى
                  </span>
                </p>

                {/* Stats Badges */}
                <div className="mt-12 flex flex-wrap justify-center gap-6 animate-scale-in" style={{animationDelay: '0.6s'}}>
                  <div className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-green-500/20 to-emerald-500/20 backdrop-blur-sm border border-green-400/30 rounded-2xl px-6 py-4 group hover:scale-105 transition-transform">
                    <div className="w-4 h-4 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                    <span className="text-white font-semibold">+1M مسافر سعيد</span>
                  </div>
                  <div className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-yellow-500/20 to-orange-500/20 backdrop-blur-sm border border-yellow-400/30 rounded-2xl px-6 py-4 group hover:scale-105 transition-transform">
                    <div className="w-4 h-4 bg-yellow-400 rounded-full animate-pulse shadow-lg shadow-yellow-400/50"></div>
                    <span className="text-white font-semibold">أفضل الأسعار</span>
                  </div>
                  <div className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-blue-400/30 rounded-2xl px-6 py-4 group hover:scale-105 transition-transform">
                    <div className="w-4 h-4 bg-blue-400 rounded-full animate-pulse shadow-lg shadow-blue-400/50"></div>
                    <span className="text-white font-semibold">دعم 24/7</span>
                  </div>
                </div>
              </div>

              {/* Search Form */}
              <div className="animate-slide-up" style={{animationDelay: '0.9s'}}>
                <SearchForm />
              </div>
            </div>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="flex flex-col items-center space-y-2">
              <span className="text-white/70 text-sm font-medium">اكتشف المزيد</span>
              <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center relative overflow-hidden">
                <div className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-pulse"></div>
                <div className="absolute inset-0 bg-gradient-to-b from-transparent to-white/10 animate-pulse"></div>
              </div>
            </div>
          </div>

          {/* Cinematic Vignette */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute inset-0 bg-radial-gradient from-transparent via-transparent to-black/20"></div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-pattern opacity-30"></div>

          <div className="container mx-auto px-4 relative">
            <div className="text-center mb-16 animate-fade-in">
              <span className="inline-block px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                لماذا نحن الأفضل
              </span>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 arabic-display">
                لماذا تختار رحلاتي؟
              </h2>
              <p className="text-gray-600 text-xl max-w-3xl mx-auto leading-relaxed">
                نوفر لك أفضل الخدمات السياحية بأسعار تنافسية وجودة عالية مع ضمان الرضا التام
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="text-center p-8 group animate-slide-up">
                <div className="relative mb-6">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                    <Shield className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">✓</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-800">حجز آمن</h3>
                <p className="text-gray-600 leading-relaxed">
                  نضمن لك حجز آمن ومحمي بأحدث تقنيات الأمان والتشفير المتقدم
                </p>
                <div className="mt-4 w-12 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"></div>
              </div>

              <div className="text-center p-8 group animate-slide-up" style={{animationDelay: '0.1s'}}>
                <div className="relative mb-6">
                  <div className="bg-gradient-to-br from-emerald-500 to-teal-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                    <Star className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">★</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-800">أفضل الأسعار</h3>
                <p className="text-gray-600 leading-relaxed">
                  نقارن الأسعار من مئات المواقع لنوفر لك أفضل العروض والخصومات الحصرية
                </p>
                <div className="mt-4 w-12 h-1 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full mx-auto"></div>
              </div>

              <div className="text-center p-8 group animate-slide-up" style={{animationDelay: '0.2s'}}>
                <div className="relative mb-6">
                  <div className="bg-gradient-to-br from-orange-500 to-red-500 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                    <Clock className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">24</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-800">دعم 24/7</h3>
                <p className="text-gray-600 leading-relaxed">
                  فريق الدعم متاح على مدار الساعة لمساعدتك في أي وقت وأي مكان
                </p>
                <div className="mt-4 w-12 h-1 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mx-auto"></div>
              </div>

              <div className="text-center p-8 group animate-slide-up" style={{animationDelay: '0.3s'}}>
                <div className="relative mb-6">
                  <div className="bg-gradient-to-br from-purple-500 to-pink-600 w-20 h-20 rounded-2xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg group-hover:shadow-xl">
                    <Users className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">10+</span>
                  </div>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-800">خبرة واسعة</h3>
                <p className="text-gray-600 leading-relaxed">
                  أكثر من 10 سنوات من الخبرة في مجال السياحة والسفر مع فريق محترف
                </p>
                <div className="mt-4 w-12 h-1 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full mx-auto"></div>
              </div>
            </div>

            {/* Stats Section */}
            <div className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 animate-scale-in">
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-blue-600 mb-2">1M+</div>
                <div className="text-gray-600 font-medium">مسافر سعيد</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-emerald-600 mb-2">50K+</div>
                <div className="text-gray-600 font-medium">رحلة محجوزة</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-orange-600 mb-2">200+</div>
                <div className="text-gray-600 font-medium">وجهة سياحية</div>
              </div>
              <div className="text-center">
                <div className="text-4xl md:text-5xl font-bold text-purple-600 mb-2">24/7</div>
                <div className="text-gray-600 font-medium">دعم فني</div>
              </div>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className="py-20 bg-white relative overflow-hidden">
          {/* Background Elements */}
          <div className="absolute top-0 left-0 w-64 h-64 bg-blue-100 rounded-full -translate-x-32 -translate-y-32 opacity-50"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-purple-100 rounded-full translate-x-48 translate-y-48 opacity-30"></div>

          <div className="container mx-auto px-4 relative">
            <div className="text-center mb-16 animate-fade-in">
              <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                خدماتنا المميزة
              </span>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 arabic-display">
                خدماتنا الشاملة
              </h2>
              <p className="text-gray-600 text-xl max-w-3xl mx-auto leading-relaxed">
                نوفر لك جميع احتياجاتك السياحية في مكان واحد مع أفضل الأسعار والخدمات المتميزة
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Flight Booking */}
              <div className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 animate-slide-up border border-gray-100 overflow-hidden">
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-purple-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative text-center">
                  <div className="relative mb-8">
                    <div className="bg-gradient-to-br from-blue-500 to-purple-600 w-24 h-24 rounded-3xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg group-hover:shadow-xl">
                      <Plane className="h-12 w-12 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center animate-pulse">
                      <span className="text-white text-xs font-bold">✈</span>
                    </div>
                  </div>

                  <h3 className="text-3xl font-bold mb-4 text-gray-800 group-hover:text-blue-600 transition-colors">حجز الطيران</h3>
                  <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                    احجز تذاكر الطيران لجميع الوجهات العالمية بأفضل الأسعار والعروض الحصرية
                  </p>

                  <div className="space-y-4 mb-8">
                    <div className="flex items-center space-x-3 space-x-reverse bg-blue-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">رحلات داخلية وخارجية</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-blue-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">جميع شركات الطيران</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-blue-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">إلغاء مجاني</span>
                    </div>
                  </div>

                  <button className="btn-primary w-full transform group-hover:scale-105 transition-transform">
                    احجز رحلتك الآن
                  </button>

                  <div className="mt-4 text-sm text-gray-500">
                    ابتداءً من <span className="font-bold text-blue-600">299 ريال</span>
                  </div>
                </div>
              </div>

              {/* Hotel Booking */}
              <div className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 animate-slide-up border border-gray-100 overflow-hidden" style={{animationDelay: '0.1s'}}>
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-50 to-teal-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative text-center">
                  <div className="relative mb-8">
                    <div className="bg-gradient-to-br from-emerald-500 to-teal-600 w-24 h-24 rounded-3xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg group-hover:shadow-xl">
                      <Hotel className="h-12 w-12 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
                      <span className="text-white text-xs font-bold">🏨</span>
                    </div>
                  </div>

                  <h3 className="text-3xl font-bold mb-4 text-gray-800 group-hover:text-emerald-600 transition-colors">حجز الفنادق</h3>
                  <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                    اختر من بين آلاف الفنادق حول العالم بأسعار مميزة وخدمات فاخرة
                  </p>

                  <div className="space-y-4 mb-8">
                    <div className="flex items-center space-x-3 space-x-reverse bg-emerald-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">فنادق 3-5 نجوم</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-emerald-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">إفطار مجاني</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-emerald-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"></div>
                      <span className="font-medium text-gray-700">إلغاء مرن</span>
                    </div>
                  </div>

                  <button className="btn-secondary w-full transform group-hover:scale-105 transition-transform">
                    احجز فندقك الآن
                  </button>

                  <div className="mt-4 text-sm text-gray-500">
                    ابتداءً من <span className="font-bold text-emerald-600">150 ريال</span> / ليلة
                  </div>
                </div>
              </div>

              {/* Car Rental */}
              <div className="group relative bg-white rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 animate-slide-up border border-gray-100 overflow-hidden" style={{animationDelay: '0.2s'}}>
                {/* Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-red-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative text-center">
                  <div className="relative mb-8">
                    <div className="bg-gradient-to-br from-orange-500 to-red-500 w-24 h-24 rounded-3xl flex items-center justify-center mx-auto mb-4 transform group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg group-hover:shadow-xl">
                      <Car className="h-12 w-12 text-white" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center animate-pulse">
                      <span className="text-white text-xs font-bold">🚗</span>
                    </div>
                  </div>

                  <h3 className="text-3xl font-bold mb-4 text-gray-800 group-hover:text-orange-600 transition-colors">تأجير السيارات</h3>
                  <p className="text-gray-600 mb-8 leading-relaxed text-lg">
                    استأجر سيارة مناسبة لرحلتك من أفضل شركات التأجير العالمية
                  </p>

                  <div className="space-y-4 mb-8">
                    <div className="flex items-center space-x-3 space-x-reverse bg-orange-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                      <span className="font-medium text-gray-700">جميع أنواع السيارات</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-orange-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                      <span className="font-medium text-gray-700">تأمين شامل</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse bg-orange-50 rounded-xl p-3 group-hover:bg-white transition-colors">
                      <div className="w-3 h-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-full"></div>
                      <span className="font-medium text-gray-700">استلام من المطار</span>
                    </div>
                  </div>

                  <button className="btn-accent w-full transform group-hover:scale-105 transition-transform">
                    استأجر سيارتك الآن
                  </button>

                  <div className="mt-4 text-sm text-gray-500">
                    ابتداءً من <span className="font-bold text-orange-600">80 ريال</span> / يوم
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Popular Destinations */}
        <PopularDestinations />
      </main>

      <Footer />
    </>
  )
}

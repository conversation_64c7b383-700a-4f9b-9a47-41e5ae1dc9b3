import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'رحلاتي - منصة الحجوزات السياحية',
  description: 'احجز رحلاتك وفنادقك وسياراتك بسهولة مع منصة رحلاتي الشاملة للسياحة والسفر',
  keywords: 'حجز طيران, حجز فنادق, تأجير سيارات, سياحة, سفر, رحلات',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="arabic-text bg-gray-50 min-h-screen">
        <div className="min-h-screen flex flex-col">
          {children}
        </div>
      </body>
    </html>
  )
}

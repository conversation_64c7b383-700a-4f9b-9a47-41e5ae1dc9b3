// User Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  nationality?: string
  passportNumber?: string
  createdAt: string
  updatedAt: string
}

// Flight Types
export interface Flight {
  id: string
  airline: string
  flightNumber: string
  departure: {
    airport: string
    city: string
    country: string
    time: string
    date: string
  }
  arrival: {
    airport: string
    city: string
    country: string
    time: string
    date: string
  }
  duration: string
  price: number
  currency: string
  class: 'economy' | 'business' | 'first'
  stops: number
  aircraft: string
  availableSeats: number
}

export interface FlightSearch {
  from: string
  to: string
  departureDate: string
  returnDate?: string
  passengers: {
    adults: number
    children: number
    infants: number
  }
  class: 'economy' | 'business' | 'first'
  tripType: 'roundtrip' | 'oneway'
}

// Hotel Types
export interface Hotel {
  id: string
  name: string
  description: string
  address: string
  city: string
  country: string
  rating: number
  images: string[]
  amenities: string[]
  rooms: Room[]
  pricePerNight: number
  currency: string
  coordinates: {
    lat: number
    lng: number
  }
}

export interface Room {
  id: string
  type: string
  description: string
  maxOccupancy: number
  pricePerNight: number
  amenities: string[]
  images: string[]
  availableRooms: number
}

export interface HotelSearch {
  destination: string
  checkIn: string
  checkOut: string
  guests: number
  rooms: number
}

// Car Rental Types
export interface Car {
  id: string
  make: string
  model: string
  year: number
  category: 'economy' | 'compact' | 'suv' | 'luxury'
  transmission: 'manual' | 'automatic'
  fuelType: 'petrol' | 'diesel' | 'electric' | 'hybrid'
  seats: number
  doors: number
  airConditioning: boolean
  pricePerDay: number
  currency: string
  images: string[]
  features: string[]
  availableCars: number
}

export interface CarRentalSearch {
  pickupLocation: string
  dropoffLocation?: string
  pickupDate: string
  dropoffDate: string
  driverAge: number
}

// Booking Types
export interface Booking {
  id: string
  userId: string
  type: 'flight' | 'hotel' | 'car'
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  bookingDate: string
  totalAmount: number
  currency: string
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded'
  details: FlightBooking | HotelBooking | CarBooking
}

export interface FlightBooking {
  outboundFlight: Flight
  returnFlight?: Flight
  passengers: Passenger[]
  seats: string[]
  specialRequests?: string
}

export interface HotelBooking {
  hotel: Hotel
  room: Room
  checkIn: string
  checkOut: string
  nights: number
  guests: number
  specialRequests?: string
}

export interface CarBooking {
  car: Car
  pickupLocation: string
  dropoffLocation: string
  pickupDate: string
  dropoffDate: string
  days: number
  driverDetails: {
    name: string
    licenseNumber: string
    licenseExpiry: string
  }
  insurance: string[]
  extras: string[]
}

export interface Passenger {
  title: 'mr' | 'mrs' | 'ms'
  firstName: string
  lastName: string
  dateOfBirth: string
  nationality: string
  passportNumber: string
  passportExpiry: string
  type: 'adult' | 'child' | 'infant'
}

// Payment Types
export interface Payment {
  id: string
  bookingId: string
  amount: number
  currency: string
  method: 'card' | 'bank_transfer' | 'wallet'
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  transactionId?: string
  createdAt: string
  updatedAt: string
}

// Destination Types
export interface Destination {
  id: string
  name: string
  country: string
  description: string
  images: string[]
  attractions: string[]
  bestTimeToVisit: string
  averageTemperature: {
    min: number
    max: number
  }
  currency: string
  language: string
  timeZone: string
  popularActivities: string[]
  averageCost: {
    budget: number
    midRange: number
    luxury: number
  }
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form Types
export interface ContactForm {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
}

export interface NewsletterSubscription {
  email: string
  preferences: string[]
}

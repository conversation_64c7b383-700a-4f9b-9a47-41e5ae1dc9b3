import Header from '@/components/Header'
import Footer from '@/components/Footer'
import { Plane, Filter, Calendar, Users, ArrowUpDown } from 'lucide-react'

export default function FlightsPage() {
  return (
    <>
      <Header />
      
      <main className="flex-1 bg-gray-50">
        {/* Page Header */}
        <section className="bg-primary-600 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="text-center">
              <h1 className="text-3xl md:text-4xl font-bold mb-4">
                حجز تذاكر الطيران
              </h1>
              <p className="text-xl text-blue-100">
                اعثر على أفضل العروض لرحلاتك الجوية
              </p>
            </div>
          </div>
        </section>

        {/* Search Results */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              {/* Filters Sidebar */}
              <div className="lg:w-1/4">
                <div className="card">
                  <div className="flex items-center space-x-2 space-x-reverse mb-4">
                    <Filter className="h-5 w-5 text-primary-600" />
                    <h3 className="text-lg font-semibold">تصفية النتائج</h3>
                  </div>
                  
                  {/* Price Range */}
                  <div className="mb-6">
                    <h4 className="font-medium mb-3">نطاق السعر</h4>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>أقل من 500 ريال</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>500 - 1000 ريال</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>1000 - 2000 ريال</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>أكثر من 2000 ريال</span>
                      </label>
                    </div>
                  </div>

                  {/* Airlines */}
                  <div className="mb-6">
                    <h4 className="font-medium mb-3">شركات الطيران</h4>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>الخطوط السعودية</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>طيران الإمارات</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>القطرية</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>الاتحاد للطيران</span>
                      </label>
                    </div>
                  </div>

                  {/* Departure Time */}
                  <div className="mb-6">
                    <h4 className="font-medium mb-3">وقت المغادرة</h4>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>الصباح (6:00 - 12:00)</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>بعد الظهر (12:00 - 18:00)</span>
                      </label>
                      <label className="flex items-center space-x-2 space-x-reverse">
                        <input type="checkbox" className="text-primary-600" />
                        <span>المساء (18:00 - 24:00)</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Results */}
              <div className="lg:w-3/4">
                {/* Sort Options */}
                <div className="flex items-center justify-between mb-6">
                  <p className="text-gray-600">تم العثور على 24 رحلة</p>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-sm text-gray-600">ترتيب حسب:</span>
                    <select className="border border-gray-300 rounded-lg px-3 py-1 text-sm">
                      <option>السعر (الأقل أولاً)</option>
                      <option>المدة (الأقصر أولاً)</option>
                      <option>وقت المغادرة</option>
                      <option>شركة الطيران</option>
                    </select>
                  </div>
                </div>

                {/* Flight Results */}
                <div className="space-y-4">
                  {/* Flight Card 1 */}
                  <div className="card hover:shadow-lg transition-shadow">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                      <div className="flex-1 mb-4 md:mb-0">
                        <div className="flex items-center space-x-4 space-x-reverse mb-3">
                          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                            <Plane className="h-6 w-6 text-primary-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">الخطوط السعودية</h3>
                            <p className="text-gray-600 text-sm">SV 123</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 items-center">
                          <div className="text-center">
                            <p className="text-2xl font-bold">08:30</p>
                            <p className="text-gray-600">الرياض (RUH)</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse">
                              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <ArrowUpDown className="h-4 w-4 text-gray-400 rotate-90" />
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">3 ساعات 45 دقيقة</p>
                            <p className="text-xs text-gray-500">مباشر</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">12:15</p>
                            <p className="text-gray-600">دبي (DXB)</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-center md:text-right">
                        <p className="text-3xl font-bold text-primary-600 mb-2">1,250 ريال</p>
                        <p className="text-sm text-gray-600 mb-4">للشخص الواحد</p>
                        <button className="btn-primary w-full md:w-auto px-6">
                          اختيار الرحلة
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Flight Card 2 */}
                  <div className="card hover:shadow-lg transition-shadow">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                      <div className="flex-1 mb-4 md:mb-0">
                        <div className="flex items-center space-x-4 space-x-reverse mb-3">
                          <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                            <Plane className="h-6 w-6 text-secondary-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">طيران الإمارات</h3>
                            <p className="text-gray-600 text-sm">EK 456</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 items-center">
                          <div className="text-center">
                            <p className="text-2xl font-bold">14:20</p>
                            <p className="text-gray-600">الرياض (RUH)</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse">
                              <div className="w-2 h-2 bg-secondary-600 rounded-full"></div>
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <ArrowUpDown className="h-4 w-4 text-gray-400 rotate-90" />
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <div className="w-2 h-2 bg-secondary-600 rounded-full"></div>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">3 ساعات 30 دقيقة</p>
                            <p className="text-xs text-gray-500">مباشر</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">17:50</p>
                            <p className="text-gray-600">دبي (DXB)</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-center md:text-right">
                        <p className="text-3xl font-bold text-secondary-600 mb-2">1,450 ريال</p>
                        <p className="text-sm text-gray-600 mb-4">للشخص الواحد</p>
                        <button className="btn-secondary w-full md:w-auto px-6">
                          اختيار الرحلة
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Flight Card 3 */}
                  <div className="card hover:shadow-lg transition-shadow">
                    <div className="flex flex-col md:flex-row items-start md:items-center justify-between">
                      <div className="flex-1 mb-4 md:mb-0">
                        <div className="flex items-center space-x-4 space-x-reverse mb-3">
                          <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
                            <Plane className="h-6 w-6 text-accent-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">الخطوط القطرية</h3>
                            <p className="text-gray-600 text-sm">QR 789</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 items-center">
                          <div className="text-center">
                            <p className="text-2xl font-bold">20:15</p>
                            <p className="text-gray-600">الرياض (RUH)</p>
                          </div>
                          <div className="text-center">
                            <div className="flex items-center justify-center space-x-2 space-x-reverse">
                              <div className="w-2 h-2 bg-accent-600 rounded-full"></div>
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <ArrowUpDown className="h-4 w-4 text-gray-400 rotate-90" />
                              <div className="flex-1 h-px bg-gray-300"></div>
                              <div className="w-2 h-2 bg-accent-600 rounded-full"></div>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">4 ساعات 15 دقيقة</p>
                            <p className="text-xs text-gray-500">توقف في الدوحة</p>
                          </div>
                          <div className="text-center">
                            <p className="text-2xl font-bold">00:30</p>
                            <p className="text-gray-600">دبي (DXB)</p>
                            <p className="text-xs text-gray-500">+1 يوم</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-center md:text-right">
                        <p className="text-3xl font-bold text-accent-600 mb-2">980 ريال</p>
                        <p className="text-sm text-gray-600 mb-4">للشخص الواحد</p>
                        <button className="btn-outline w-full md:w-auto px-6">
                          اختيار الرحلة
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pagination */}
                <div className="flex justify-center mt-8">
                  <div className="flex space-x-2 space-x-reverse">
                    <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">السابق</button>
                    <button className="px-3 py-2 bg-primary-600 text-white rounded-lg">1</button>
                    <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">2</button>
                    <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">3</button>
                    <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">التالي</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </>
  )
}

'use client'

import { useState } from 'react'
import { 
  Plane, 
  MapPin, 
  Calendar, 
  Star, 
  Heart, 
  Camera, 
  Compass, 
  Globe,
  ArrowRight,
  TrendingUp,
  Award,
  Users
} from 'lucide-react'

const travelCategories = [
  {
    id: 1,
    title: 'رحلات العائلة',
    description: 'وجهات مثالية للعائلات مع أنشطة ممتعة للجميع',
    icon: Users,
    color: 'from-blue-500 to-cyan-500',
    image: 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=400&h=250&fit=crop',
    destinations: ['دبي', 'أنطاليا', 'شرم الشيخ']
  },
  {
    id: 2,
    title: 'رحلات رومانسية',
    description: 'أجمل الوجهات للأزواج والعشاق',
    icon: Heart,
    color: 'from-pink-500 to-rose-500',
    image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=400&h=250&fit=crop',
    destinations: ['باريس', 'البندقية', 'سانتوريني']
  },
  {
    id: 3,
    title: 'مغامرات وطبيعة',
    description: 'استكشف الطبيعة الخلابة والمغامرات المثيرة',
    icon: Compass,
    color: 'from-green-500 to-emerald-500',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=250&fit=crop',
    destinations: ['نيوزيلندا', 'النرويج', 'كوستاريكا']
  },
  {
    id: 4,
    title: 'تراث وثقافة',
    description: 'اكتشف التاريخ والثقافات العريقة',
    icon: Camera,
    color: 'from-purple-500 to-indigo-500',
    image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=250&fit=crop',
    destinations: ['روما', 'القاهرة', 'كيوتو']
  }
]

const travelTips = [
  {
    id: 1,
    title: 'أفضل وقت للحجز',
    description: 'احجز رحلاتك قبل 6-8 أسابيع للحصول على أفضل الأسعار',
    icon: Calendar,
    tip: 'وفر حتى 40%'
  },
  {
    id: 2,
    title: 'السفر في المواسم المنخفضة',
    description: 'تجنب المواسم السياحية للحصول على أسعار أقل وتجربة أفضل',
    icon: TrendingUp,
    tip: 'وفر حتى 60%'
  },
  {
    id: 3,
    title: 'برامج الولاء',
    description: 'انضم لبرامج الولاء لشركات الطيران والفنادق',
    icon: Award,
    tip: 'مزايا حصرية'
  },
  {
    id: 4,
    title: 'التأمين على السفر',
    description: 'احم رحلتك بتأمين شامل ضد الإلغاء والطوارئ',
    icon: Globe,
    tip: 'راحة بال'
  }
]

export default function DiscoverMore() {
  const [activeCategory, setActiveCategory] = useState(1)
  const [hoveredTip, setHoveredTip] = useState<number | null>(null)

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-blue-100 rounded-full -translate-y-48 translate-x-48 opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-100 rounded-full translate-y-32 -translate-x-32 opacity-40"></div>
      
      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 rounded-full text-sm font-medium mb-4">
            اكتشف المزيد
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 arabic-title">
            استكشف عالم السفر
          </h2>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto leading-relaxed arabic-text">
            اكتشف أفضل الوجهات السياحية ونصائح السفر لتجربة لا تُنسى
          </p>
        </div>

        {/* Travel Categories */}
        <div className="mb-20">
          <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center arabic-title">
            اختر نوع رحلتك
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {travelCategories.map((category, index) => {
              const IconComponent = category.icon
              return (
                <div
                  key={category.id}
                  className={`group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 cursor-pointer animate-slide-up ${
                    activeCategory === category.id ? 'ring-4 ring-blue-500/50' : ''
                  }`}
                  style={{animationDelay: `${index * 0.1}s`}}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {/* Image */}
                  <div className="relative h-48 overflow-hidden">
                    <div 
                      className="absolute inset-0 bg-cover bg-center transform group-hover:scale-110 transition-transform duration-700"
                      style={{backgroundImage: `url(${category.image})`}}
                    ></div>
                    <div className={`absolute inset-0 bg-gradient-to-t ${category.color} opacity-60 group-hover:opacity-40 transition-opacity duration-300`}></div>
                    
                    {/* Icon */}
                    <div className="absolute top-4 right-4 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    
                    {/* Category Badge */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <h4 className="text-xl font-bold text-white mb-1 arabic-title">{category.title}</h4>
                      <p className="text-white/90 text-sm arabic-text">{category.description}</p>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <div className="flex flex-wrap gap-2 mb-4">
                      {category.destinations.map((dest, idx) => (
                        <span 
                          key={idx}
                          className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium arabic-text"
                        >
                          {dest}
                        </span>
                      ))}
                    </div>
                    
                    <button className="w-full btn-outline group/btn">
                      <span className="flex items-center justify-center space-x-2 space-x-reverse">
                        <span className="arabic-text">استكشف الآن</span>
                        <ArrowRight className="h-4 w-4 group-hover/btn:-translate-x-1 transition-transform" />
                      </span>
                    </button>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Travel Tips */}
        <div>
          <h3 className="text-3xl font-bold text-gray-800 mb-8 text-center arabic-title">
            نصائح السفر الذكية
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {travelTips.map((tip, index) => {
              const IconComponent = tip.icon
              return (
                <div
                  key={tip.id}
                  className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 animate-slide-up"
                  style={{animationDelay: `${index * 0.1}s`}}
                  onMouseEnter={() => setHoveredTip(tip.id)}
                  onMouseLeave={() => setHoveredTip(null)}
                >
                  <div className="flex items-center space-x-4 space-x-reverse mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-transform">
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-800 arabic-title">{tip.title}</h4>
                      <span className="text-sm text-blue-600 font-medium">{tip.tip}</span>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 leading-relaxed arabic-text">
                    {tip.description}
                  </p>
                  
                  {hoveredTip === tip.id && (
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <button className="text-blue-600 hover:text-blue-700 font-medium text-sm arabic-text">
                        اقرأ المزيد ←
                      </button>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 md:p-12 text-white relative overflow-hidden">
            <div className="absolute inset-0 bg-pattern opacity-10"></div>
            <div className="relative">
              <h3 className="text-3xl md:text-4xl font-bold mb-4 arabic-title">
                جاهز لبدء رحلتك القادمة؟
              </h3>
              <p className="text-xl text-blue-100 mb-8 arabic-text">
                انضم إلى أكثر من مليون مسافر واكتشف العالم معنا
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="btn-secondary bg-white text-blue-600 hover:bg-blue-50">
                  ابدأ التخطيط الآن
                </button>
                <button className="btn-outline border-white text-white hover:bg-white hover:text-blue-600">
                  تصفح العروض الخاصة
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

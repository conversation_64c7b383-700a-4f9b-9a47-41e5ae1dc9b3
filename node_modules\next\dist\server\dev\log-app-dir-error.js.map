{"version": 3, "sources": ["../../../src/server/dev/log-app-dir-error.ts"], "names": ["logAppDirError", "err", "isError", "stack", "cleanedStack", "split", "map", "line", "replace", "filteredStack", "filter", "test", "length", "Log", "error", "join", "digest", "console", "JSON", "stringify", "cause"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;gEAHI;6DACC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,SAASA,eAAeC,GAAY;IACzC,IAAIC,IAAAA,gBAAO,EAACD,SAAQA,uBAAAA,IAAKE,KAAK,GAAE;QAC9B,MAAMC,eAAeH,IAAIE,KAAK,CAACE,KAAK,CAAC,MAAMC,GAAG,CAAC,CAACC,OAC9C,iDAAiD;YACjDA,KAAKC,OAAO,CAAC,kDAAkD;QAEjE,MAAMC,gBAAgBL,YACpB,iDAAiD;SAChDM,MAAM,CACL,CAACH,OACC,CAAC,6BAA6BI,IAAI,CAACJ,SACnC,CAAC,oBAAoBI,IAAI,CAACJ,SAC1B,CAAC,qBAAqBI,IAAI,CAACJ;QAEjC,IAAIE,cAAcG,MAAM,KAAK,GAAG;YAC9B,uEAAuE;YACvEC,KAAIC,KAAK,CAAC,CAAC,gBAAgB,EAAEV,aAAaW,IAAI,CAAC,MAAM,CAAC;QACxD,OAAO;YACLF,KAAIC,KAAK,CAACL,cAAcM,IAAI,CAAC;QAC/B;QACA,IAAI,OAAO,AAACd,IAAYe,MAAM,KAAK,aAAa;YAC9CC,QAAQH,KAAK,CAAC,CAAC,QAAQ,EAAEI,KAAKC,SAAS,CAAC,AAAClB,IAAYe,MAAM,EAAE,CAAC;QAChE;QAEA,IAAIf,IAAImB,KAAK,EAAEH,QAAQH,KAAK,CAAC,UAAUb,IAAImB,KAAK;IAClD,OAAO;QACLP,KAAIC,KAAK,CAACb;IACZ;AACF"}
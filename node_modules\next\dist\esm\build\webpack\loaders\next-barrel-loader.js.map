{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-barrel-loader.ts"], "names": ["path", "transform", "barrelTransformMappingCache", "Map", "getBarrelMapping", "resourcePath", "swcCacheDir", "resolve", "fs", "has", "get", "transpileSource", "filename", "source", "isWildcard", "isTypeScript", "endsWith", "Promise", "res", "inputSourceMap", "undefined", "sourceFileName", "optimizeBarrelExports", "wildcard", "jsc", "parser", "syntax", "experimental", "cacheRoot", "then", "output", "code", "visited", "Set", "getMatches", "file", "isClientEntry", "add", "rej", "readFile", "err", "data", "toString", "matches", "match", "matchedDirectives", "directiveList", "JSON", "parse", "includes", "exportList", "slice", "wildcardExports", "matchAll", "map", "decl", "length", "all", "req", "targetPath", "dirname", "replace", "targetMatches", "concat", "set", "NextBarrelLoader", "async", "cacheable", "names", "getOptions", "getResolve", "mainFields", "mapping", "clearDependencies", "callback", "stringify", "exportMap", "name", "filePath", "orig", "missedNames", "push", "join"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoFC,GAID,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,YAAW;AAErC,iFAAiF;AACjF,mFAAmF;AACnF,+DAA+D;AAC/D,kEAAkE;AAClE,MAAMC,8BAA8B,IAAIC;AASxC,eAAeC,iBACbC,YAAoB,EACpBC,WAAmB,EACnBC,OAA8D,EAC9DC,EAKC;IAED,IAAIN,4BAA4BO,GAAG,CAACJ,eAAe;QACjD,OAAOH,4BAA4BQ,GAAG,CAACL;IACzC;IAEA,6EAA6E;IAC7E,mDAAmD;IACnD,eAAeM,gBACbC,QAAgB,EAChBC,MAAc,EACdC,UAAmB;QAEnB,MAAMC,eAAeH,SAASI,QAAQ,CAAC,UAAUJ,SAASI,QAAQ,CAAC;QACnE,OAAO,IAAIC,QAAgB,CAACC,MAC1BjB,UAAUY,QAAQ;gBAChBD;gBACAO,gBAAgBC;gBAChBC,gBAAgBT;gBAChBU,uBAAuB;oBACrBC,UAAUT;gBACZ;gBACAU,KAAK;oBACHC,QAAQ;wBACNC,QAAQX,eAAe,eAAe;wBACtC,CAACA,eAAe,QAAQ,MAAM,EAAE;oBAClC;oBACAY,cAAc;wBACZC,WAAWtB;oBACb;gBACF;YACF,GAAGuB,IAAI,CAAC,CAACC;gBACPZ,IAAIY,OAAOC,IAAI;YACjB;IAEJ;IAEA,yCAAyC;IACzC,MAAMC,UAAU,IAAIC;IACpB,eAAeC,WACbC,IAAY,EACZrB,UAAmB,EACnBsB,aAAsB;QAEtB,IAAIJ,QAAQvB,GAAG,CAAC0B,OAAO;YACrB,OAAO;QACT;QACAH,QAAQK,GAAG,CAACF;QAEZ,MAAMtB,SAAS,MAAM,IAAII,QAAgB,CAACC,KAAKoB;YAC7C9B,GAAG+B,QAAQ,CAACJ,MAAM,CAACK,KAAKC;gBACtB,IAAID,OAAOC,SAASrB,WAAW;oBAC7BkB,IAAIE;gBACN,OAAO;oBACLtB,IAAIuB,KAAKC,QAAQ;gBACnB;YACF;QACF;QAEA,MAAMZ,SAAS,MAAMnB,gBAAgBwB,MAAMtB,QAAQC;QAEnD,MAAM6B,UAAUb,OAAOc,KAAK,CAC1B;QAEF,IAAI,CAACD,SAAS;YACZ,OAAO;QACT;QAEA,MAAME,oBAAoBf,OAAOc,KAAK,CACpC;QAEF,MAAME,gBAAgBD,oBAClBE,KAAKC,KAAK,CAACH,iBAAiB,CAAC,EAAE,IAC/B,EAAE;QACN,yEAAyE;QACzET,gBAAgBU,cAAcG,QAAQ,CAAC;QAEvC,IAAIC,aAAaH,KAAKC,KAAK,CAACL,OAAO,CAAC,EAAE,CAACQ,KAAK,CAAC,GAAG,CAAC;QAKjD,MAAMC,kBAAkB;eACnBtB,OAAOuB,QAAQ,CAAC;SACpB,CAACC,GAAG,CAAC,CAACV,QAAUA,KAAK,CAAC,EAAE;QAEzB,uEAAuE;QACvE,sEAAsE;QACtE,eAAe;QACf,IAAI9B,YAAY;YACd,KAAK,MAAMyC,QAAQL,WAAY;gBAC7BK,IAAI,CAAC,EAAE,GAAGpB;gBACVoB,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;YACnB;QACF;QAEA,6EAA6E;QAC7E,IAAIH,gBAAgBI,MAAM,EAAE;YAC1B,MAAMvC,QAAQwC,GAAG,CACfL,gBAAgBE,GAAG,CAAC,OAAOI;gBACzB,MAAMC,aAAa,MAAMpD,QACvBP,KAAK4D,OAAO,CAACzB,OACbuB,IAAIG,OAAO,CAAC,gDAAgD;gBAG9D,MAAMC,gBAAgB,MAAM5B,WAC1ByB,YACA,MACAvB;gBAEF,IAAI0B,eAAe;oBACjB,wBAAwB;oBACxBZ,aAAaA,WAAWa,MAAM,CAACD,cAAcZ,UAAU;gBACzD;YACF;QAEJ;QAEA,OAAO;YACLA;YACAE;YACAhB;QACF;IACF;IAEA,MAAMlB,MAAM,MAAMgB,WAAW7B,cAAc,OAAO;IAClDH,4BAA4B8D,GAAG,CAAC3D,cAAca;IAE9C,OAAOA;AACT;AAEA,MAAM+C,mBAAmB;IAMvB,IAAI,CAACC,KAAK;IACV,IAAI,CAACC,SAAS,CAAC;IAEf,MAAM,EAAEC,KAAK,EAAE9D,WAAW,EAAE,GAAG,IAAI,CAAC+D,UAAU;IAE9C,yEAAyE;IACzE,6EAA6E;IAC7E,MAAM9D,UAAU,IAAI,CAAC+D,UAAU,CAAC;QAC9BC,YAAY;YAAC;YAAU;SAAO;IAChC;IAEA,MAAMC,UAAU,MAAMpE,iBACpB,IAAI,CAACC,YAAY,EACjBC,aACAC,SACA,IAAI,CAACC,EAAE;IAGT,4EAA4E;IAC5E,yEAAyE;IACzE,6EAA6E;IAC7E,wBAAwB;IACxB,IAAI,CAACiE,iBAAiB;IAEtB,IAAI,CAACD,SAAS;QACZ,6FAA6F;QAC7F,2FAA2F;QAC3F,0FAA0F;QAC1F,+BAA+B;QAC/B,IAAI,CAACE,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE3B,KAAK4B,SAAS,CAAC,IAAI,CAACtE,YAAY,EAAE,CAAC;QACxE;IACF;IAEA,MAAM6C,aAAasB,QAAQtB,UAAU;IACrC,MAAMd,gBAAgBoC,QAAQpC,aAAa;IAC3C,MAAMwC,YAAY,IAAIzE;IACtB,KAAK,MAAM,CAAC0E,MAAMC,UAAUC,KAAK,IAAI7B,WAAY;QAC/C0B,UAAUZ,GAAG,CAACa,MAAM;YAACC;YAAUC;SAAK;IACtC;IAEA,IAAIjD,SAAS;IACb,IAAIkD,cAAwB,EAAE;IAC9B,KAAK,MAAMH,QAAQT,MAAO;QACxB,sBAAsB;QACtB,IAAIQ,UAAUnE,GAAG,CAACoE,OAAO;YACvB,MAAMtB,OAAOqB,UAAUlE,GAAG,CAACmE;YAE3B,IAAItB,IAAI,CAAC,EAAE,KAAK,KAAK;gBACnBzB,UAAU,CAAC,cAAc,EAAE+C,KAAK,MAAM,EAAE9B,KAAK4B,SAAS,CAACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YACnE,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAK,WAAW;gBAChCzB,UAAU,CAAC,sBAAsB,EAAE+C,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CAC9DpB,IAAI,CAAC,EAAE,EACP,CAAC;YACL,OAAO,IAAIA,IAAI,CAAC,EAAE,KAAKsB,MAAM;gBAC3B/C,UAAU,CAAC,WAAW,EAAE+C,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CAACpB,IAAI,CAAC,EAAE,EAAE,CAAC;YAClE,OAAO;gBACLzB,UAAU,CAAC,WAAW,EAAEyB,IAAI,CAAC,EAAE,CAAC,IAAI,EAAEsB,KAAK,QAAQ,EAAE9B,KAAK4B,SAAS,CACjEpB,IAAI,CAAC,EAAE,EACP,CAAC;YACL;QACF,OAAO;YACLyB,YAAYC,IAAI,CAACJ;QACnB;IACF;IAEA,mCAAmC;IACnC,IAAIG,YAAYxB,MAAM,GAAG,GAAG;QAC1B,KAAK,MAAME,OAAOc,QAAQpB,eAAe,CAAE;YACzCtB,UAAU,CAAC,gBAAgB,EAAEiB,KAAK4B,SAAS,CACzCjB,IAAIG,OAAO,CAAC,mBAAmBmB,YAAYE,IAAI,CAAC,OAAO,cACvD,CAAC;QACL;IACF;IAEA,yEAAyE;IACzE,yCAAyC;IACzC,IAAI9C,eAAe;QACjBN,SAAS,CAAC,eAAe,EAAEA,OAAO,CAAC;IACrC;IAEA,IAAI,CAAC4C,QAAQ,CAAC,MAAM5C;AACtB;AAEA,eAAemC,iBAAgB"}
'use client'

import { useState } from 'react'
import { MapPin, Star, Heart, ArrowLeft } from 'lucide-react'

const destinations = [
  {
    id: 1,
    name: 'دبي',
    country: 'الإمارات العربية المتحدة',
    price: 1200,
    rating: 4.8,
    image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=500&h=300&fit=crop',
    description: 'مدينة المستقبل والتسوق الفاخر',
    highlights: ['برج خليفة', 'دبي مول', 'نافورة دبي'],
    gradient: 'from-blue-500 to-cyan-500'
  },
  {
    id: 2,
    name: 'القاهرة',
    country: 'مصر',
    price: 800,
    rating: 4.6,
    image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=500&h=300&fit=crop',
    description: 'أم الدنيا وعاصمة التاريخ والحضارة',
    highlights: ['الأهرامات', 'المتحف المصري', 'خان الخليلي'],
    gradient: 'from-yellow-500 to-orange-500'
  },
  {
    id: 3,
    name: 'إسطنبول',
    country: 'تركيا',
    price: 1000,
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b?w=500&h=300&fit=crop',
    description: 'ملتقى القارات وجسر بين الشرق والغرب',
    highlights: ['آيا صوفيا', 'البازار الكبير', 'مضيق البوسفور'],
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    id: 4,
    name: 'الرياض',
    country: 'المملكة العربية السعودية',
    price: 600,
    rating: 4.5,
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=300&fit=crop',
    description: 'عاصمة المملكة ومركز الأعمال',
    highlights: ['برج المملكة', 'المربع', 'وادي حنيفة'],
    gradient: 'from-green-500 to-emerald-500'
  },
  {
    id: 5,
    name: 'باريس',
    country: 'فرنسا',
    price: 2500,
    rating: 4.9,
    image: 'https://images.unsplash.com/photo-1502602898536-47ad22581b52?w=500&h=300&fit=crop',
    description: 'مدينة النور والرومانسية',
    highlights: ['برج إيفل', 'متحف اللوفر', 'الشانزليزيه'],
    gradient: 'from-rose-500 to-pink-500'
  },
  {
    id: 6,
    name: 'لندن',
    country: 'المملكة المتحدة',
    price: 2200,
    rating: 4.7,
    image: 'https://images.unsplash.com/photo-1513635269975-59663e0ac1ad?w=500&h=300&fit=crop',
    description: 'العاصمة التاريخية والثقافية',
    highlights: ['بيغ بن', 'عين لندن', 'قصر باكنغهام'],
    gradient: 'from-indigo-500 to-blue-500'
  }
]

export default function PopularDestinations() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)
  const [likedDestinations, setLikedDestinations] = useState<number[]>([])

  const toggleLike = (id: number) => {
    setLikedDestinations(prev => 
      prev.includes(id) 
        ? prev.filter(destId => destId !== id)
        : [...prev, id]
    )
  }

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-0 right-0 w-96 h-96 bg-blue-100 rounded-full -translate-y-48 translate-x-48 opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-64 h-64 bg-purple-100 rounded-full translate-y-32 -translate-x-32 opacity-40"></div>
      
      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-600 rounded-full text-sm font-medium mb-4">
            اكتشف العالم
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 arabic-display">
            الوجهات الشائعة
          </h2>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto leading-relaxed">
            اكتشف أجمل الوجهات السياحية حول العالم واحجز رحلتك القادمة بأفضل الأسعار
          </p>
        </div>

        {/* Destinations Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {destinations.map((destination, index) => (
            <div
              key={destination.id}
              className="group relative bg-white rounded-3xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-4 animate-slide-up"
              style={{animationDelay: `${index * 0.1}s`}}
              onMouseEnter={() => setHoveredCard(destination.id)}
              onMouseLeave={() => setHoveredCard(null)}
            >
              {/* Image Container */}
              <div className="relative h-64 overflow-hidden">
                <div 
                  className="absolute inset-0 bg-cover bg-center transform group-hover:scale-110 transition-transform duration-700"
                  style={{backgroundImage: `url(${destination.image})`}}
                ></div>
                <div className={`absolute inset-0 bg-gradient-to-t ${destination.gradient} opacity-60 group-hover:opacity-40 transition-opacity duration-300`}></div>
                
                {/* Overlay Content */}
                <div className="absolute inset-0 p-6 flex flex-col justify-between">
                  {/* Top Row */}
                  <div className="flex justify-between items-start">
                    <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1 space-x-reverse">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-white font-medium text-sm">{destination.rating}</span>
                    </div>
                    <button
                      onClick={() => toggleLike(destination.id)}
                      className="bg-white/20 backdrop-blur-sm rounded-full p-2 hover:bg-white/30 transition-colors"
                    >
                      <Heart 
                        className={`h-5 w-5 transition-colors ${
                          likedDestinations.includes(destination.id) 
                            ? 'text-red-500 fill-current' 
                            : 'text-white'
                        }`} 
                      />
                    </button>
                  </div>

                  {/* Bottom Content */}
                  <div className="text-white">
                    <h3 className="text-2xl font-bold mb-1">{destination.name}</h3>
                    <div className="flex items-center space-x-2 space-x-reverse mb-2">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm opacity-90">{destination.country}</span>
                    </div>
                    <p className="text-sm opacity-80 mb-3">{destination.description}</p>
                    <div className="text-2xl font-bold">
                      ابتداءً من {destination.price} ريال
                    </div>
                  </div>
                </div>
              </div>

              {/* Card Content */}
              <div className="p-6">
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-800 mb-2">أبرز المعالم:</h4>
                  <div className="flex flex-wrap gap-2">
                    {destination.highlights.map((highlight, idx) => (
                      <span 
                        key={idx}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                </div>

                <button className="w-full btn-primary group/btn relative overflow-hidden">
                  <span className="relative z-10 flex items-center justify-center space-x-2 space-x-reverse">
                    <span>احجز الآن</span>
                    <ArrowLeft className="h-4 w-4 group-hover/btn:-translate-x-1 transition-transform" />
                  </span>
                </button>
              </div>

              {/* Hover Effect */}
              {hoveredCard === destination.id && (
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none"></div>
              )}
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center mt-12 animate-scale-in">
          <button className="btn-outline px-8 py-4 text-lg">
            عرض جميع الوجهات
          </button>
        </div>
      </div>
    </section>
  )
}

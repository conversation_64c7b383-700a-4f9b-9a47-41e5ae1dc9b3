'use client'

import { useState } from 'react'
import { Search, Plane, Calendar, Users, MapPin } from 'lucide-react'

type SearchType = 'flights' | 'hotels' | 'cars'

export default function SearchForm() {
  const [searchType, setSearchType] = useState<SearchType>('flights')
  const [tripType, setTripType] = useState<'roundtrip' | 'oneway'>('roundtrip')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle search logic here
    console.log('Search submitted')
  }

  return (
    <div className="search-form max-w-6xl mx-auto">
      {/* Search Type Tabs */}
      <div className="flex space-x-1 space-x-reverse mb-6 bg-gray-100 p-1 rounded-lg">
        <button
          onClick={() => setSearchType('flights')}
          className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-md transition-all ${
            searchType === 'flights'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          <Plane className="h-4 w-4" />
          <span>الطيران</span>
        </button>
        <button
          onClick={() => setSearchType('hotels')}
          className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-md transition-all ${
            searchType === 'hotels'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          <MapPin className="h-4 w-4" />
          <span>الفنادق</span>
        </button>
        <button
          onClick={() => setSearchType('cars')}
          className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-md transition-all ${
            searchType === 'cars'
              ? 'bg-white text-primary-600 shadow-sm'
              : 'text-gray-600 hover:text-gray-800'
          }`}
        >
          <Users className="h-4 w-4" />
          <span>السيارات</span>
        </button>
      </div>

      <form onSubmit={handleSearch}>
        {/* Flight Search */}
        {searchType === 'flights' && (
          <div className="space-y-4">
            {/* Trip Type */}
            <div className="flex space-x-4 space-x-reverse">
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  name="tripType"
                  value="roundtrip"
                  checked={tripType === 'roundtrip'}
                  onChange={(e) => setTripType(e.target.value as 'roundtrip')}
                  className="text-primary-600"
                />
                <span>ذهاب وعودة</span>
              </label>
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  name="tripType"
                  value="oneway"
                  checked={tripType === 'oneway'}
                  onChange={(e) => setTripType(e.target.value as 'oneway')}
                  className="text-primary-600"
                />
                <span>ذهاب فقط</span>
              </label>
            </div>

            {/* Search Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">من</label>
                <input
                  type="text"
                  placeholder="مدينة المغادرة"
                  className="input-field"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">إلى</label>
                <input
                  type="text"
                  placeholder="مدينة الوصول"
                  className="input-field"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">تاريخ المغادرة</label>
                <input
                  type="date"
                  className="input-field"
                />
              </div>
              {tripType === 'roundtrip' && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">تاريخ العودة</label>
                  <input
                    type="date"
                    className="input-field"
                  />
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">البالغون</label>
                <select className="input-field">
                  <option value="1">1 بالغ</option>
                  <option value="2">2 بالغ</option>
                  <option value="3">3 بالغ</option>
                  <option value="4">4 بالغ</option>
                </select>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">الأطفال</label>
                <select className="input-field">
                  <option value="0">بدون أطفال</option>
                  <option value="1">1 طفل</option>
                  <option value="2">2 طفل</option>
                  <option value="3">3 طفل</option>
                </select>
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">درجة السفر</label>
                <select className="input-field">
                  <option value="economy">الاقتصادية</option>
                  <option value="business">رجال الأعمال</option>
                  <option value="first">الأولى</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Hotel Search */}
        {searchType === 'hotels' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">الوجهة</label>
              <input
                type="text"
                placeholder="المدينة أو الفندق"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الوصول</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ المغادرة</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">الضيوف</label>
              <select className="input-field">
                <option value="1">1 ضيف</option>
                <option value="2">2 ضيف</option>
                <option value="3">3 ضيف</option>
                <option value="4">4 ضيف</option>
              </select>
            </div>
          </div>
        )}

        {/* Car Rental Search */}
        {searchType === 'cars' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">مكان الاستلام</label>
              <input
                type="text"
                placeholder="المدينة أو المطار"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الاستلام</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الإرجاع</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">نوع السيارة</label>
              <select className="input-field">
                <option value="economy">اقتصادية</option>
                <option value="compact">مدمجة</option>
                <option value="suv">SUV</option>
                <option value="luxury">فاخرة</option>
              </select>
            </div>
          </div>
        )}

        {/* Search Button */}
        <div className="flex justify-center mt-6">
          <button
            type="submit"
            className="btn-primary flex items-center space-x-2 space-x-reverse px-8 py-3 text-lg"
          >
            <Search className="h-5 w-5" />
            <span>البحث</span>
          </button>
        </div>
      </form>
    </div>
  )
}

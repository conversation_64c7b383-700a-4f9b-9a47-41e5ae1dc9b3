'use client'

import { useState } from 'react'
import { Search, Plane, Calendar, Users, MapPin, Star } from 'lucide-react'

type SearchType = 'flights' | 'hotels' | 'cars'

export default function SearchForm() {
  const [searchType, setSearchType] = useState<SearchType>('flights')
  const [tripType, setTripType] = useState<'roundtrip' | 'oneway'>('roundtrip')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle search logic here
    console.log('Search submitted')
  }

  return (
    <div className="search-form max-w-6xl mx-auto">
      {/* Search Type Tabs */}
      <div className="flex space-x-1 space-x-reverse mb-8 bg-gradient-to-r from-slate-800/80 to-blue-900/80 backdrop-blur-lg p-2 rounded-2xl border border-blue-500/30 shadow-2xl">
        <button
          onClick={() => setSearchType('flights')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-4 rounded-xl transition-all duration-300 font-semibold arabic-text ${
            searchType === 'flights'
              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl transform scale-105 border border-blue-400/50'
              : 'text-blue-200 hover:text-white hover:bg-white/10 hover:scale-102'
          }`}
        >
          <Plane className="h-5 w-5" />
          <span>الطيران</span>
        </button>
        <button
          onClick={() => setSearchType('hotels')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-4 rounded-xl transition-all duration-300 font-semibold arabic-text ${
            searchType === 'hotels'
              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl transform scale-105 border border-blue-400/50'
              : 'text-blue-200 hover:text-white hover:bg-white/10 hover:scale-102'
          }`}
        >
          <MapPin className="h-5 w-5" />
          <span>الفنادق</span>
        </button>
        <button
          onClick={() => setSearchType('cars')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-4 rounded-xl transition-all duration-300 font-semibold arabic-text ${
            searchType === 'cars'
              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-xl transform scale-105 border border-blue-400/50'
              : 'text-blue-200 hover:text-white hover:bg-white/10 hover:scale-102'
          }`}
        >
          <Users className="h-5 w-5" />
          <span>السيارات</span>
        </button>
      </div>

      <form onSubmit={handleSearch} className="bg-gradient-to-br from-slate-800/90 to-blue-900/90 backdrop-blur-lg rounded-3xl p-8 border border-blue-500/30 shadow-2xl">
        {/* Flight Search */}
        {searchType === 'flights' && (
          <div className="space-y-6">
            {/* Trip Type */}
            <div className="flex space-x-4 space-x-reverse mb-8">
              <label className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-xl px-6 py-4 border border-blue-400/30 hover:bg-blue-500/30 transition-all cursor-pointer group">
                <input
                  type="radio"
                  name="tripType"
                  value="roundtrip"
                  checked={tripType === 'roundtrip'}
                  onChange={(e) => setTripType(e.target.value as 'roundtrip')}
                  className="text-blue-400 focus:ring-blue-400 w-4 h-4"
                />
                <span className="text-white font-semibold arabic-text text-lg group-hover:text-blue-200 transition-colors">ذهاب وعودة</span>
              </label>
              <label className="flex items-center space-x-3 space-x-reverse bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm rounded-xl px-6 py-4 border border-blue-400/30 hover:bg-blue-500/30 transition-all cursor-pointer group">
                <input
                  type="radio"
                  name="tripType"
                  value="oneway"
                  checked={tripType === 'oneway'}
                  onChange={(e) => setTripType(e.target.value as 'oneway')}
                  className="text-blue-400 focus:ring-blue-400 w-4 h-4"
                />
                <span className="text-white font-semibold arabic-text text-lg group-hover:text-blue-200 transition-colors">ذهاب فقط</span>
              </label>
            </div>

            {/* Search Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">من</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="مدينة المغادرة"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white placeholder-blue-200/80 focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Plane className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">إلى</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="مدينة الوصول"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white placeholder-blue-200/80 focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <MapPin className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">تاريخ المغادرة</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              {tripType === 'roundtrip' && (
                <div className="space-y-4">
                  <label className="block text-base font-bold text-white arabic-text">تاريخ العودة</label>
                  <div className="relative">
                    <input
                      type="date"
                      className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center">
                      <Calendar className="h-5 w-5 text-blue-300" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">البالغون</label>
                <div className="relative">
                  <select className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text appearance-none font-medium shadow-lg">
                    <option value="1" className="bg-slate-800 text-white">1 بالغ</option>
                    <option value="2" className="bg-slate-800 text-white">2 بالغ</option>
                    <option value="3" className="bg-slate-800 text-white">3 بالغ</option>
                    <option value="4" className="bg-slate-800 text-white">4 بالغ</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">الأطفال</label>
                <div className="relative">
                  <select className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text appearance-none font-medium shadow-lg">
                    <option value="0" className="bg-slate-800 text-white">بدون أطفال</option>
                    <option value="1" className="bg-slate-800 text-white">1 طفل</option>
                    <option value="2" className="bg-slate-800 text-white">2 طفل</option>
                    <option value="3" className="bg-slate-800 text-white">3 طفل</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">درجة السفر</label>
                <div className="relative">
                  <select className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text appearance-none font-medium shadow-lg">
                    <option value="economy" className="bg-slate-800 text-white">الاقتصادية</option>
                    <option value="business" className="bg-slate-800 text-white">رجال الأعمال</option>
                    <option value="first" className="bg-slate-800 text-white">الأولى</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Star className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hotel Search */}
        {searchType === 'hotels' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">الوجهة</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="المدينة أو الفندق"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white placeholder-blue-200/80 focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <MapPin className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">تاريخ الوصول</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">تاريخ المغادرة</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">الضيوف</label>
                <div className="relative">
                  <select className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text appearance-none font-medium shadow-lg">
                    <option value="1" className="bg-slate-800 text-white">1 ضيف</option>
                    <option value="2" className="bg-slate-800 text-white">2 ضيف</option>
                    <option value="3" className="bg-slate-800 text-white">3 ضيف</option>
                    <option value="4" className="bg-slate-800 text-white">4 ضيف</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Car Rental Search */}
        {searchType === 'cars' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">مكان الاستلام</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="المدينة أو المطار"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white placeholder-blue-200/80 focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <MapPin className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">تاريخ الاستلام</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">تاريخ الإرجاع</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text font-medium shadow-lg"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <label className="block text-base font-bold text-white arabic-text">نوع السيارة</label>
                <div className="relative">
                  <select className="w-full px-5 py-4 bg-white/20 backdrop-blur-sm border-2 border-blue-400/40 rounded-xl text-white focus:ring-2 focus:ring-blue-300 focus:border-blue-300 outline-none transition-all duration-300 arabic-text appearance-none font-medium shadow-lg">
                    <option value="economy" className="bg-slate-800 text-white">اقتصادية</option>
                    <option value="compact" className="bg-slate-800 text-white">مدمجة</option>
                    <option value="suv" className="bg-slate-800 text-white">SUV</option>
                    <option value="luxury" className="bg-slate-800 text-white">فاخرة</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-5 w-5 text-blue-300" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Search Button */}
        <div className="flex justify-center mt-8">
          <button
            type="submit"
            className="group relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-12 rounded-2xl shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            <div className="relative flex items-center space-x-3 space-x-reverse">
              <Search className="h-6 w-6 group-hover:scale-110 transition-transform" />
              <span className="text-xl arabic-text">ابحث عن رحلتك المثالية</span>
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            </div>
          </button>
        </div>

        {/* Quick Search Suggestions */}
        <div className="mt-8 text-center">
          <p className="text-white/80 mb-4 arabic-text">وجهات شائعة:</p>
          <div className="flex flex-wrap justify-center gap-3">
            {['دبي', 'القاهرة', 'إسطنبول', 'الرياض', 'باريس', 'لندن'].map((destination) => (
              <button
                key={destination}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-full text-white/90 hover:text-white transition-all duration-300 hover:scale-105 arabic-text"
                onClick={() => {
                  // Handle quick destination search
                  console.log('Quick search for:', destination)
                }}
              >
                {destination}
              </button>
            ))}
          </div>
        </div>
      </form>
    </div>
  )
}

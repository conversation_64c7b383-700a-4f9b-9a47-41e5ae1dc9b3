'use client'

import { useState } from 'react'
import { Search, Plane, Calendar, Users, MapPin, Star } from 'lucide-react'

type SearchType = 'flights' | 'hotels' | 'cars'

export default function SearchForm() {
  const [searchType, setSearchType] = useState<SearchType>('flights')
  const [tripType, setTripType] = useState<'roundtrip' | 'oneway'>('roundtrip')

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle search logic here
    console.log('Search submitted')
  }

  return (
    <div className="search-form max-w-6xl mx-auto">
      {/* Search Type Tabs */}
      <div className="flex space-x-1 space-x-reverse mb-8 bg-white/10 backdrop-blur-lg p-1 rounded-2xl border border-white/20">
        <button
          onClick={() => setSearchType('flights')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-3 rounded-xl transition-all font-medium arabic-text ${
            searchType === 'flights'
              ? 'bg-white text-blue-600 shadow-lg transform scale-105'
              : 'text-white/80 hover:text-white hover:bg-white/10'
          }`}
        >
          <Plane className="h-5 w-5" />
          <span>الطيران</span>
        </button>
        <button
          onClick={() => setSearchType('hotels')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-3 rounded-xl transition-all font-medium arabic-text ${
            searchType === 'hotels'
              ? 'bg-white text-blue-600 shadow-lg transform scale-105'
              : 'text-white/80 hover:text-white hover:bg-white/10'
          }`}
        >
          <MapPin className="h-5 w-5" />
          <span>الفنادق</span>
        </button>
        <button
          onClick={() => setSearchType('cars')}
          className={`flex items-center space-x-2 space-x-reverse px-6 py-3 rounded-xl transition-all font-medium arabic-text ${
            searchType === 'cars'
              ? 'bg-white text-blue-600 shadow-lg transform scale-105'
              : 'text-white/80 hover:text-white hover:bg-white/10'
          }`}
        >
          <Users className="h-5 w-5" />
          <span>السيارات</span>
        </button>
      </div>

      <form onSubmit={handleSearch}>
        {/* Flight Search */}
        {searchType === 'flights' && (
          <div className="space-y-4">
            {/* Trip Type */}
            <div className="flex space-x-4 space-x-reverse">
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  name="tripType"
                  value="roundtrip"
                  checked={tripType === 'roundtrip'}
                  onChange={(e) => setTripType(e.target.value as 'roundtrip')}
                  className="text-primary-600"
                />
                <span>ذهاب وعودة</span>
              </label>
              <label className="flex items-center space-x-2 space-x-reverse">
                <input
                  type="radio"
                  name="tripType"
                  value="oneway"
                  checked={tripType === 'oneway'}
                  onChange={(e) => setTripType(e.target.value as 'oneway')}
                  className="text-primary-600"
                />
                <span>ذهاب فقط</span>
              </label>
            </div>

            {/* Search Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">من</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="مدينة المغادرة"
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Plane className="h-5 w-5 text-white/60" />
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">إلى</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="مدينة الوصول"
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <MapPin className="h-5 w-5 text-white/60" />
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">تاريخ المغادرة</label>
                <div className="relative">
                  <input
                    type="date"
                    className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text"
                  />
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Calendar className="h-5 w-5 text-white/60" />
                  </div>
                </div>
              </div>
              {tripType === 'roundtrip' && (
                <div className="space-y-3">
                  <label className="block text-sm font-semibold text-white/90 arabic-text">تاريخ العودة</label>
                  <div className="relative">
                    <input
                      type="date"
                      className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text"
                    />
                    <div className="absolute inset-y-0 left-3 flex items-center">
                      <Calendar className="h-5 w-5 text-white/60" />
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">البالغون</label>
                <div className="relative">
                  <select className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text appearance-none">
                    <option value="1" className="bg-gray-800 text-white">1 بالغ</option>
                    <option value="2" className="bg-gray-800 text-white">2 بالغ</option>
                    <option value="3" className="bg-gray-800 text-white">3 بالغ</option>
                    <option value="4" className="bg-gray-800 text-white">4 بالغ</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-5 w-5 text-white/60" />
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">الأطفال</label>
                <div className="relative">
                  <select className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text appearance-none">
                    <option value="0" className="bg-gray-800 text-white">بدون أطفال</option>
                    <option value="1" className="bg-gray-800 text-white">1 طفل</option>
                    <option value="2" className="bg-gray-800 text-white">2 طفل</option>
                    <option value="3" className="bg-gray-800 text-white">3 طفل</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Users className="h-4 w-4 text-white/60" />
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <label className="block text-sm font-semibold text-white/90 arabic-text">درجة السفر</label>
                <div className="relative">
                  <select className="w-full px-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-400 focus:border-transparent outline-none transition-all duration-300 arabic-text appearance-none">
                    <option value="economy" className="bg-gray-800 text-white">الاقتصادية</option>
                    <option value="business" className="bg-gray-800 text-white">رجال الأعمال</option>
                    <option value="first" className="bg-gray-800 text-white">الأولى</option>
                  </select>
                  <div className="absolute inset-y-0 left-3 flex items-center">
                    <Star className="h-5 w-5 text-white/60" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Hotel Search */}
        {searchType === 'hotels' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">الوجهة</label>
              <input
                type="text"
                placeholder="المدينة أو الفندق"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الوصول</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ المغادرة</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">الضيوف</label>
              <select className="input-field">
                <option value="1">1 ضيف</option>
                <option value="2">2 ضيف</option>
                <option value="3">3 ضيف</option>
                <option value="4">4 ضيف</option>
              </select>
            </div>
          </div>
        )}

        {/* Car Rental Search */}
        {searchType === 'cars' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">مكان الاستلام</label>
              <input
                type="text"
                placeholder="المدينة أو المطار"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الاستلام</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">تاريخ الإرجاع</label>
              <input
                type="date"
                className="input-field"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">نوع السيارة</label>
              <select className="input-field">
                <option value="economy">اقتصادية</option>
                <option value="compact">مدمجة</option>
                <option value="suv">SUV</option>
                <option value="luxury">فاخرة</option>
              </select>
            </div>
          </div>
        )}

        {/* Search Button */}
        <div className="flex justify-center mt-8">
          <button
            type="submit"
            className="group relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-12 rounded-2xl shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
            <div className="relative flex items-center space-x-3 space-x-reverse">
              <Search className="h-6 w-6 group-hover:scale-110 transition-transform" />
              <span className="text-xl arabic-text">ابحث عن رحلتك المثالية</span>
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
            </div>
          </button>
        </div>

        {/* Quick Search Suggestions */}
        <div className="mt-8 text-center">
          <p className="text-white/80 mb-4 arabic-text">وجهات شائعة:</p>
          <div className="flex flex-wrap justify-center gap-3">
            {['دبي', 'القاهرة', 'إسطنبول', 'الرياض', 'باريس', 'لندن'].map((destination) => (
              <button
                key={destination}
                className="px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-full text-white/90 hover:text-white transition-all duration-300 hover:scale-105 arabic-text"
                onClick={() => {
                  // Handle quick destination search
                  console.log('Quick search for:', destination)
                }}
              >
                {destination}
              </button>
            ))}
          </div>
        </div>
      </form>
    </div>
  )
}

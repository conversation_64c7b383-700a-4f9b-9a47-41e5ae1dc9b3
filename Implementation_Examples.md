# Implementation Examples and Code Snippets
## أمثلة التنفيذ ومقاطع الكود

This document provides practical implementation examples for the Arabic travel website development plan.

## 1. Project Initialization

### Next.js Setup with Arabic Support
```bash
# Create Next.js project
npx create-next-app@latest arabic-travel-site --typescript --tailwind --eslint --app

# Install required dependencies
npm install next-i18next react-i18next
npm install @headlessui/react @heroicons/react
npm install react-hook-form @hookform/resolvers zod
npm install zustand
npm install dayjs
npm install @types/node

# Install development dependencies
npm install -D @types/react @types/react-dom
npm install -D eslint-config-prettier prettier
npm install -D husky lint-staged
```

### Tailwind CSS RTL Configuration
```javascript
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'arabic': ['Noto Sans Arabic', 'Arial', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
  // Enable RTL support
  corePlugins: {
    direction: true,
  },
}
```

### Next.js i18n Configuration
```javascript
// next.config.js
const { i18n } = require('./next-i18next.config')

/** @type {import('next').NextConfig} */
const nextConfig = {
  i18n,
  experimental: {
    appDir: true,
  },
  images: {
    domains: ['example.com', 'images.unsplash.com'],
  },
}

module.exports = nextConfig
```

```javascript
// next-i18next.config.js
module.exports = {
  i18n: {
    defaultLocale: 'ar',
    locales: ['ar', 'en'],
    localeDetection: false,
  },
  reloadOnPrerender: process.env.NODE_ENV === 'development',
  localePath: './public/locales',
}
```

## 2. Core Components

### RTL Layout Component
```typescript
// components/layout/RTLLayout.tsx
import { ReactNode } from 'react'
import { useRouter } from 'next/router'
import Head from 'next/head'

interface RTLLayoutProps {
  children: ReactNode
  title?: string
}

export default function RTLLayout({ children, title }: RTLLayoutProps) {
  const router = useRouter()
  const isRTL = router.locale === 'ar'

  return (
    <>
      <Head>
        <title>{title || 'موقع السفر العربي'}</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </Head>
      <div 
        dir={isRTL ? 'rtl' : 'ltr'} 
        className={`min-h-screen font-arabic ${isRTL ? 'text-right' : 'text-left'}`}
      >
        {children}
      </div>
    </>
  )
}
```

### Arabic Navigation Component
```typescript
// components/navigation/Navbar.tsx
import { useState } from 'react'
import Link from 'next/link'
import { useTranslation } from 'next-i18next'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'

export default function Navbar() {
  const { t } = useTranslation('common')
  const [isOpen, setIsOpen] = useState(false)

  const navigation = [
    { name: t('flights'), href: '/flights' },
    { name: t('hotels'), href: '/hotels' },
    { name: t('packages'), href: '/packages' },
    { name: t('car-rental'), href: '/car-rental' },
    { name: t('destinations'), href: '/destinations' },
  ]

  return (
    <nav className="bg-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex-shrink-0">
              <img className="h-8 w-auto" src="/logo.svg" alt={t('site-name')} />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8 rtl:space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
            <Link
              href="/login"
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              {t('login')}
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-gray-700 hover:text-blue-600"
            >
              {isOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-700 hover:text-blue-600 block px-3 py-2 text-base font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
```

### Flight Search Component
```typescript
// components/flights/FlightSearch.tsx
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'next-i18next'
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline'

const flightSearchSchema = z.object({
  from: z.string().min(3, 'يجب اختيار مطار المغادرة'),
  to: z.string().min(3, 'يجب اختيار مطار الوصول'),
  departureDate: z.string().min(1, 'يجب اختيار تاريخ المغادرة'),
  returnDate: z.string().optional(),
  passengers: z.number().min(1).max(9),
  class: z.enum(['economy', 'business', 'first']),
  tripType: z.enum(['oneWay', 'roundTrip']),
})

type FlightSearchForm = z.infer<typeof flightSearchSchema>

export default function FlightSearch() {
  const { t } = useTranslation('flights')
  const [tripType, setTripType] = useState<'oneWay' | 'roundTrip'>('roundTrip')

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<FlightSearchForm>({
    resolver: zodResolver(flightSearchSchema),
    defaultValues: {
      passengers: 1,
      class: 'economy',
      tripType: 'roundTrip',
    },
  })

  const onSubmit = (data: FlightSearchForm) => {
    console.log('Flight search:', data)
    // Handle flight search logic
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {t('search-flights')}
        </h2>
        
        {/* Trip Type Toggle */}
        <div className="flex space-x-4 rtl:space-x-reverse mb-4">
          <label className="flex items-center">
            <input
              type="radio"
              value="roundTrip"
              checked={tripType === 'roundTrip'}
              onChange={(e) => setTripType(e.target.value as 'roundTrip')}
              className="ml-2 rtl:ml-0 rtl:mr-2"
            />
            {t('round-trip')}
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              value="oneWay"
              checked={tripType === 'oneWay'}
              onChange={(e) => setTripType(e.target.value as 'oneWay')}
              className="ml-2 rtl:ml-0 rtl:mr-2"
            />
            {t('one-way')}
          </label>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* From Airport */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('from')}
            </label>
            <input
              {...register('from')}
              type="text"
              placeholder={t('departure-city')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.from && (
              <p className="text-red-500 text-sm mt-1">{errors.from.message}</p>
            )}
          </div>

          {/* To Airport */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('to')}
            </label>
            <input
              {...register('to')}
              type="text"
              placeholder={t('destination-city')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.to && (
              <p className="text-red-500 text-sm mt-1">{errors.to.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Departure Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('departure-date')}
            </label>
            <input
              {...register('departureDate')}
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.departureDate && (
              <p className="text-red-500 text-sm mt-1">{errors.departureDate.message}</p>
            )}
          </div>

          {/* Return Date */}
          {tripType === 'roundTrip' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('return-date')}
              </label>
              <input
                {...register('returnDate')}
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {/* Passengers */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('passengers')}
            </label>
            <select
              {...register('passengers', { valueAsNumber: true })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
                <option key={num} value={num}>
                  {num} {num === 1 ? t('passenger') : t('passengers')}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Class Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {t('class')}
          </label>
          <select
            {...register('class')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="economy">{t('economy')}</option>
            <option value="business">{t('business')}</option>
            <option value="first">{t('first-class')}</option>
          </select>
        </div>

        {/* Search Button */}
        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 rtl:space-x-reverse"
        >
          <MagnifyingGlassIcon className="h-5 w-5" />
          <span>{t('search-flights')}</span>
        </button>
      </form>
    </div>
  )
}
```

## 3. Localization Files

### Arabic Translation File
```json
// public/locales/ar/common.json
{
  "site-name": "موقع السفر العربي",
  "flights": "رحلات طيران",
  "hotels": "فنادق",
  "packages": "باقات سفر",
  "car-rental": "تأجير سيارات",
  "destinations": "وجهات سفر",
  "login": "تسجيل الدخول",
  "register": "إنشاء حساب",
  "search": "بحث",
  "book-now": "احجز الآن",
  "view-details": "عرض التفاصيل",
  "price": "السعر",
  "currency": "ريال سعودي",
  "loading": "جاري التحميل...",
  "error": "حدث خطأ",
  "success": "تم بنجاح"
}
```

```json
// public/locales/ar/flights.json
{
  "search-flights": "البحث عن رحلات طيران",
  "round-trip": "ذهاب وعودة",
  "one-way": "ذهاب فقط",
  "from": "من",
  "to": "إلى",
  "departure-city": "مدينة المغادرة",
  "destination-city": "مدينة الوصول",
  "departure-date": "تاريخ المغادرة",
  "return-date": "تاريخ العودة",
  "passengers": "المسافرون",
  "passenger": "مسافر",
  "class": "الدرجة",
  "economy": "اقتصادية",
  "business": "رجال أعمال",
  "first-class": "درجة أولى",
  "flight-results": "نتائج البحث",
  "no-flights-found": "لم يتم العثور على رحلات",
  "select-flight": "اختر الرحلة",
  "flight-details": "تفاصيل الرحلة",
  "duration": "مدة الرحلة",
  "stops": "التوقفات",
  "direct": "مباشر",
  "airline": "شركة الطيران"
}
```

## 4. Database Models

### User Model (Prisma Schema)
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  passwordHash  String?
  firstName     String?
  lastName      String?
  phone         String?
  dateOfBirth   DateTime?
  nationality   String?
  preferredLang String    @default("ar")
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  bookings      Booking[]
  reviews       Review[]
  
  @@map("users")
}

model Flight {
  id              String   @id @default(cuid())
  airlineCode     String
  flightNumber    String
  departureAirport String
  arrivalAirport  String
  departureTime   DateTime
  arrivalTime     DateTime
  aircraftType    String?
  price           Decimal
  currency        String   @default("USD")
  availableSeats  Int
  classType       String
  createdAt       DateTime @default(now())
  
  bookings        FlightBooking[]
  
  @@map("flights")
}

model Booking {
  id                String   @id @default(cuid())
  userId            String
  bookingType       String   // 'flight', 'hotel', 'package'
  bookingReference  String   @unique
  status            String   @default("pending")
  totalAmount       Decimal
  currency          String   @default("USD")
  bookingDate       DateTime @default(now())
  travelDate        DateTime?
  passengers        Json?
  paymentStatus     String   @default("pending")
  createdAt         DateTime @default(now())
  
  user              User     @relation(fields: [userId], references: [id])
  flightBookings    FlightBooking[]
  hotelBookings     HotelBooking[]
  
  @@map("bookings")
}

model FlightBooking {
  id        String @id @default(cuid())
  bookingId String
  flightId  String
  seatNumber String?
  
  booking   Booking @relation(fields: [bookingId], references: [id])
  flight    Flight  @relation(fields: [flightId], references: [id])
  
  @@map("flight_bookings")
}
```

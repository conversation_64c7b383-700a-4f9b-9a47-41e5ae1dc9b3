{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "names": ["getNotFoundError", "getImageError", "getModuleTrace", "input", "compilation", "visitedModules", "Set", "moduleTrace", "current", "module", "has", "add", "origin", "moduleGraph", "get<PERSON><PERSON><PERSON>", "push", "getSourceFrame", "fileName", "result", "loc", "dependencies", "map", "d", "filter", "Boolean", "originalSource", "createOriginalStackFrame", "line", "start", "column", "source", "rootDirectory", "options", "context", "modulePath", "frame", "originalCodeFrame", "lineNumber", "originalStackFrame", "toString", "getFormattedFileName", "loaders", "find", "loader", "test", "JSON", "parse", "resourceResolveData", "query", "slice", "path", "formattedFileName", "cyan", "yellow", "name", "message", "errorMessage", "error", "replace", "green", "importTrace", "readableIdentifier", "requestShortener", "length", "join", "red", "bold", "SimpleWebpackError", "err", "page", "rawRequest", "importedFile", "buffer", "split", "some", "includes", "concat"], "mappings": ";;;;;;;;;;;;;;;IAmGsBA,gBAAgB;eAAhBA;;IAqEAC,aAAa;eAAbA;;;4BAxKyB;oCACZ;4BACM;AAGzC,6IAA6I;AAC7I;;;;;;;;;;;;;;;;;;;;;;AAsBA,GACA,SAASC,eAAeC,KAAU,EAAEC,WAAgB;IAClD,MAAMC,iBAAiB,IAAIC;IAC3B,MAAMC,cAAc,EAAE;IACtB,IAAIC,UAAUL,MAAMM,MAAM;IAC1B,MAAOD,QAAS;QACd,IAAIH,eAAeK,GAAG,CAACF,UAAU,OAAM,mDAAmD;QAC1FH,eAAeM,GAAG,CAACH;QACnB,MAAMI,SAASR,YAAYS,WAAW,CAACC,SAAS,CAACN;QACjD,IAAI,CAACI,QAAQ;QACbL,YAAYQ,IAAI,CAAC;YAAEH;YAAQH,QAAQD;QAAQ;QAC3CA,UAAUI;IACZ;IAEA,OAAOL;AACT;AAEA,eAAeS,eACbb,KAAU,EACVc,QAAa,EACbb,WAAgB;IAEhB,IAAI;YAiBYc,uCAAAA,4BACJA,mCAAAA;QAjBV,MAAMC,MAAMhB,MAAMgB,GAAG,GACjBhB,MAAMgB,GAAG,GACThB,MAAMiB,YAAY,CAACC,GAAG,CAAC,CAACC,IAAWA,EAAEH,GAAG,EAAEI,MAAM,CAACC,QAAQ,CAAC,EAAE;QAChE,MAAMC,iBAAiBtB,MAAMM,MAAM,CAACgB,cAAc;QAElD,MAAMP,SAAS,MAAMQ,IAAAA,oCAAwB,EAAC;YAC5CC,MAAMR,IAAIS,KAAK,CAACD,IAAI;YACpBE,QAAQV,IAAIS,KAAK,CAACC,MAAM;YACxBC,QAAQL;YACRM,eAAe3B,YAAY4B,OAAO,CAACC,OAAO;YAC1CC,YAAYjB;YACZkB,OAAO,CAAC;QACV;QAEA,OAAO;YACLA,OAAOjB,CAAAA,0BAAAA,OAAQkB,iBAAiB,KAAI;YACpCC,YAAYnB,CAAAA,2BAAAA,6BAAAA,OAAQoB,kBAAkB,sBAA1BpB,wCAAAA,2BAA4BmB,UAAU,qBAAtCnB,sCAAwCqB,QAAQ,OAAM;YAClEV,QAAQX,CAAAA,2BAAAA,8BAAAA,OAAQoB,kBAAkB,sBAA1BpB,oCAAAA,4BAA4BW,MAAM,qBAAlCX,kCAAoCqB,QAAQ,OAAM;QAC5D;IACF,EAAE,OAAM;QACN,OAAO;YAAEJ,OAAO;YAAIE,YAAY;YAAIR,QAAQ;QAAG;IACjD;AACF;AAEA,SAASW,qBACPvB,QAAgB,EAChBR,OAAW,EACX4B,UAAmB,EACnBR,MAAe;QAGbpB;IADF,KACEA,kBAAAA,QAAOgC,OAAO,qBAAdhC,gBAAgBiC,IAAI,CAAC,CAACC,SACpB,gCAAgCC,IAAI,CAACD,OAAOA,MAAM,IAEpD;QACA,mFAAmF;QACnF,2CAA2C;QAC3C,OAAOE,KAAKC,KAAK,CAACrC,QAAOsC,mBAAmB,CAACC,KAAK,CAACC,KAAK,CAAC,IAAIC,IAAI;IACnE,OAAO;QACL,IAAIC,oBAA4BC,IAAAA,gBAAI,EAACnC;QACrC,IAAIoB,cAAcR,QAAQ;YACxBsB,qBAAqB,CAAC,CAAC,EAAEE,IAAAA,kBAAM,EAAChB,YAAY,CAAC,EAAEgB,IAAAA,kBAAM,EAACxB,QAAQ,CAAC;QACjE;QAEA,OAAOsB;IACT;AACF;AAEO,eAAenD,iBACpBI,WAAgC,EAChCD,KAAU,EACVc,QAAgB,EAChBR,OAAW;IAEX,IACEN,MAAMmD,IAAI,KAAK,yBACf,CACEnD,CAAAA,MAAMmD,IAAI,KAAK,sBACf,gCAAgCV,IAAI,CAACzC,MAAMoD,OAAO,CAAA,GAEpD;QACA,OAAO;IACT;IAEA,IAAI;QACF,MAAM,EAAEpB,KAAK,EAAEE,UAAU,EAAER,MAAM,EAAE,GAAG,MAAMb,eAC1Cb,OACAc,UACAb;QAGF,MAAMoD,eAAerD,MAAMsD,KAAK,CAACF,OAAO,CACrCG,OAAO,CAAC,aAAa,IACrBA,OAAO,CAAC,wBAAwB,CAAC,eAAe,EAAEC,IAAAA,iBAAK,EAAC,MAAM,CAAC,CAAC;QAEnE,MAAMC,cAAc;YAClB,MAAMrD,cAAcL,eAAeC,OAAOC,aACvCiB,GAAG,CAAC,CAAC,EAAET,MAAM,EAAE,GACdA,OAAOiD,kBAAkB,CAACzD,YAAY0D,gBAAgB,GAEvDvC,MAAM,CACL,CAAC+B,OACCA,QACA,CAAC,0FAA0FV,IAAI,CAC7FU,SAEF,CAAC,+BAA+BV,IAAI,CAACU,SACrC,CAAC,mBAAmBV,IAAI,CAACU;YAE/B,IAAI/C,YAAYwD,MAAM,KAAK,GAAG,OAAO;YAErC,OAAO,CAAC,sCAAsC,EAAExD,YAAYyD,IAAI,CAAC,MAAM,CAAC;QAC1E;QAEA,IAAIT,UACFU,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,uBACT,CAAC,EAAE,EAAEV,aAAa,CAAC,GACnB,OACArB,QACCA,CAAAA,UAAU,KAAK,OAAO,EAAC,IACxB,0DACAyB;QAEF,MAAMT,oBAAoBX,qBACxBvB,UACAR,SACA4B,YACAR;QAGF,OAAO,IAAIsC,sCAAkB,CAAChB,mBAAmBI;IACnD,EAAE,OAAOa,KAAK;QACZ,8CAA8C;QAC9C,OAAOjE;IACT;AACF;AAEO,eAAeF,cACpBG,WAAgB,EAChBD,KAAU,EACViE,GAAU;IAEV,IAAIA,IAAId,IAAI,KAAK,2BAA2B;QAC1C,OAAO;IACT;IAEA,MAAM/C,cAAcL,eAAeC,OAAOC;IAC1C,MAAM,EAAEQ,MAAM,EAAEH,QAAAA,OAAM,EAAE,GAAGF,WAAW,CAAC,EAAE,IAAI,CAAC;IAC9C,IAAI,CAACK,UAAU,CAACH,SAAQ;QACtB,OAAO;IACT;IACA,MAAM4D,OAAOzD,OAAO0D,UAAU,CAACZ,OAAO,CAAC,uBAAuB;IAC9D,MAAMa,eAAe9D,QAAO6D,UAAU;IACtC,MAAMxC,SAASlB,OAAOa,cAAc,GAAG+C,MAAM,GAAGjC,QAAQ,CAAC;IACzD,IAAIF,aAAa,CAAC;IAClBP,OAAO2C,KAAK,CAAC,MAAMC,IAAI,CAAC,CAAC/C;QACvBU;QACA,OAAOV,KAAKgD,QAAQ,CAACJ;IACvB;IACA,OAAO,IAAIJ,sCAAkB,CAC3B,CAAC,EAAEf,IAAAA,gBAAI,EAACiB,MAAM,CAAC,EAAEhB,IAAAA,kBAAM,EAAChB,WAAWE,QAAQ,IAAI,CAAC,EAChD0B,IAAAA,eAAG,EAACC,IAAAA,gBAAI,EAAC,UAAUU,MAAM,CACvB,CAAC,gBAAgB,EAAEL,aAAa,iFAAiF,CAAC;AAGxH"}
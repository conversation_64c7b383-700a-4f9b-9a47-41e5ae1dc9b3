{"version": 3, "sources": ["../../../../src/shared/lib/lazy-dynamic/loadable.tsx"], "names": ["React", "NoSSR", "Loadable", "options", "opts", "Object", "assign", "loader", "loading", "ssr", "lazy", "LoadableComponent", "props", "Loading", "fallbackElement", "isLoading", "past<PERSON>elay", "error", "Wrap", "Fragment", "Lazy", "Suspense", "fallback", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,KAAK,QAAQ,mBAAkB;AAExC,SAASC,SAASC,OAAY;IAC5B,MAAMC,OAAOC,OAAOC,MAAM,CACxB;QACEC,QAAQ;QACRC,SAAS;QACTC,KAAK;IACP,GACAN;IAGFC,KAAKM,IAAI,iBAAGV,MAAMU,IAAI,CAACN,KAAKG,MAAM;IAElC,SAASI,kBAAkBC,KAAU;QACnC,MAAMC,UAAUT,KAAKI,OAAO;QAC5B,MAAMM,gCACJ,oBAACD;YAAQE,WAAW;YAAMC,WAAW;YAAMC,OAAO;;QAGpD,MAAMC,OAAOd,KAAKK,GAAG,GAAGT,MAAMmB,QAAQ,GAAGlB;QACzC,MAAMmB,OAAOhB,KAAKM,IAAI;QAEtB,qBACE,oBAACV,MAAMqB,QAAQ;YAACC,UAAUR;yBACxB,oBAACI,0BACC,oBAACE,MAASR;IAIlB;IAEAD,kBAAkBY,WAAW,GAAG;IAEhC,OAAOZ;AACT;AAEA,eAAeT,SAAQ"}
{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/resolve-url-loader/index.ts"], "names": ["SourceMapConsumer", "valueProcessor", "defaultJoin", "process", "resolveUrlLoader", "content", "sourceMap", "options", "Object", "assign", "silent", "absolute", "<PERSON><PERSON><PERSON><PERSON>", "root", "debug", "join", "getOptions", "sourceMapConsumer", "callback", "async", "postcss", "resourcePath", "outputSourceMap", "Boolean", "transformDeclaration", "inputSourceMap", "catch", "onFailure", "then", "onSuccess", "error", "encodeError", "reworked", "map", "label", "exception", "Error", "concat", "message", "stack", "split", "trim", "filter"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,GAEA,SAASA,iBAAiB,QAAQ,gCAA+B;AACjE,OAAOC,oBAAoB,wBAAuB;AAClD,SAASC,WAAW,QAAQ,sBAAqB;AACjD,OAAOC,aAAa,gBAAe;AACnC;;;CAGC,GACD,eAAe,eAAeC,iBAE5B,gBAAgB,GAChBC,OAAe,EACf,mBAAmB,GACnBC,SAAc;IAEd,MAAMC,UAAUC,OAAOC,MAAM,CAC3B;QACEH,WAAW,IAAI,CAACA,SAAS;QACzBI,QAAQ;QACRC,UAAU;QACVC,WAAW;QACXC,MAAM;QACNC,OAAO;QACPC,MAAMb;IACR,GACA,IAAI,CAACc,UAAU;IAGjB,IAAIC;IACJ,IAAIX,WAAW;QACbW,oBAAoB,IAAIjB,kBAAkBM;IAC5C;IAEA,MAAMY,WAAW,IAAI,CAACC,KAAK;IAC3B,MAAM,EAAEC,OAAO,EAAE,GAAG,MAAMb,QAAQa,OAAO;IACzCjB,QAAQiB,SAAS,IAAI,CAACC,YAAY,EAAEhB,SAAS;QAC3CiB,iBAAiBC,QAAQhB,QAAQD,SAAS;QAC1CkB,sBAAsBvB,eAAe,IAAI,CAACoB,YAAY,EAAEd;QACxDkB,gBAAgBnB;QAChBW,mBAAmBA;IACrB,EACE,mEAAmE;KAClES,KAAK,CAACC,UACP,mEAAmE;KAClEC,IAAI,CAACC;IAER,SAASF,UAAUG,KAAY;QAC7B,mEAAmE;QACnEZ,SAASa,YAAY,aAAaD;IACpC;IAEA,SAASD,UAAUG,QAAa;QAC9B,IAAIA,UAAU;YACZ,2BAA2B;YAC3B,+DAA+D;YAC/D,IAAIzB,QAAQD,SAAS,EAAE;gBACrBY,SAAS,MAAMc,SAAS3B,OAAO,EAAE2B,SAASC,GAAG;YAC/C,OAEK;gBACHf,SAAS,MAAMc,SAAS3B,OAAO;YACjC;QACF;IACF;IAEA,SAAS0B,YAAYG,KAAU,EAAEC,SAAc;QAC7C,OAAO,IAAIC,MACT;YACE;YACA;YACA;gBAACF;aAAM,CACJG,MAAM,CACL,AAAC,OAAOF,cAAc,YAAYA,aAC/BA,qBAAqBC,SAAS;gBAC7BD,UAAUG,OAAO;gBAChBH,UAAkBI,KAAK,CAACC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,CAACC,IAAI;aAChD,IACD,EAAE,EAELC,MAAM,CAACnB,SACPR,IAAI,CAAC;SACT,CAACA,IAAI,CAAC;IAEX;AACF"}
{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/internal/components/hot-linked-text/index.tsx"], "names": ["HotlinkedText", "linkRegex", "props", "text", "wordsAndWhitespaces", "getWordsAndWhitespaces", "test", "map", "word", "index", "React", "Fragment", "key", "a", "href"], "mappings": ";;;;+BAKaA;;;eAAAA;;;;gEALK;wCACqB;AAEvC,MAAMC,YAAY;AAEX,MAAMD,gBAER,SAASA,cAAcE,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAE,GAAGD;IAEjB,MAAME,sBAAsBC,IAAAA,8CAAsB,EAACF;IAEnD,qBACE,4DACGF,UAAUK,IAAI,CAACH,QACZC,oBAAoBG,GAAG,CAAC,CAACC,MAAMC;QAC7B,IAAIR,UAAUK,IAAI,CAACE,OAAO;YACxB,qBACE,6BAACE,cAAK,CAACC,QAAQ;gBAACC,KAAK,AAAC,UAAOH;6BAC3B,6BAACI;gBAAEC,MAAMN;eAAOA;QAGtB;QACA,qBAAO,6BAACE,cAAK,CAACC,QAAQ;YAACC,KAAK,AAAC,UAAOH;WAAUD;IAChD,KACAL;AAGV"}
{"version": 3, "sources": ["../../../src/server/og/image-response.ts"], "names": ["ImageResponse", "Response", "displayName", "constructor", "args", "readable", "ReadableStream", "start", "controller", "OGImageResponse", "process", "env", "NEXT_RUNTIME", "imageResponse", "body", "close", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "enqueue", "options", "headers", "NODE_ENV", "status", "statusText"], "mappings": ";;;;+BAAaA;;;eAAAA;;;AAAN,MAAMA,sBAAsBC;qBACnBC,cAAc;IAC5BC,YACE,GAAGC,IAEF,CACD;QACA,MAAMC,WAAW,IAAIC,eAAe;YAClC,MAAMC,OAAMC,UAAU;gBACpB,MAAMC,kBAGJ,AAFA,2DAA2D;gBAC3D,uCAAuC;gBAErC,CAAA,MAAM,MAAM,CACVC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,gDACA,8CACN,EACAZ,aAAa;gBACjB,MAAMa,gBAAgB,IAAIJ,mBAAmBL;gBAE7C,IAAI,CAACS,cAAcC,IAAI,EAAE;oBACvB,OAAON,WAAWO,KAAK;gBACzB;gBAEA,MAAMC,SAASH,cAAcC,IAAI,CAAEG,SAAS;gBAC5C,MAAO,KAAM;oBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;oBACzC,IAAIF,MAAM;wBACR,OAAOV,WAAWO,KAAK;oBACzB;oBACAP,WAAWa,OAAO,CAACF;gBACrB;YACF;QACF;QAEA,MAAMG,UAAUlB,IAAI,CAAC,EAAE,IAAI,CAAC;QAE5B,KAAK,CAACC,UAAU;YACdkB,SAAS;gBACP,gBAAgB;gBAChB,iBACEb,QAAQC,GAAG,CAACa,QAAQ,KAAK,gBACrB,uBACA;gBACN,GAAGF,QAAQC,OAAO;YACpB;YACAE,QAAQH,QAAQG,MAAM;YACtBC,YAAYJ,QAAQI,UAAU;QAChC;IACF;AACF"}
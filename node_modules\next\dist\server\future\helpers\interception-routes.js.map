{"version": 3, "sources": ["../../../../src/server/future/helpers/interception-routes.ts"], "names": ["INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "extractInterceptionRouteInformation", "path", "split", "find", "segment", "m", "startsWith", "undefined", "interceptingRoute", "marker", "interceptedRoute", "Error", "normalizeAppPath", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;;;;;;;;;IAGaA,0BAA0B;eAA1BA;;IAOGC,0BAA0B;eAA1BA;;IAWAC,mCAAmC;eAAnCA;;;0BArBiB;AAG1B,MAAMF,6BAA6B;IACxC;IACA;IACA;IACA;CACD;AAEM,SAASC,2BAA2BE,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLN,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASP,oCAAoCC,IAAY;IAC9D,IAAIO,mBACFC,QACAC;IAEF,KAAK,MAAMN,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCO,SAASX,2BAA2BK,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAII,QAAQ;YACT,CAACD,mBAAmBE,iBAAiB,GAAGT,KAAKC,KAAK,CAACO,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,IAAIC,MACR,CAAC,4BAA4B,EAAEV,KAAK,iFAAiF,CAAC;IAE1H;IAEAO,oBAAoBI,IAAAA,0BAAgB,EAACJ,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAmB,CAAC,CAAC,EAAEA,iBAAiB,CAAC;YAC3C,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,IAAIG,MACR,CAAC,4BAA4B,EAAEV,KAAK,4DAA4D,CAAC;YAErG;YACAS,mBAAmBF,kBAChBN,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIL,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMM,yBAAyBR,kBAAkBN,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,IAAIN,MACR,CAAC,4BAA4B,EAAEV,KAAK,+DAA+D,CAAC;YAExG;YAEAS,mBAAmBM,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACJ,kBACPK,IAAI,CAAC;YACR;QACF;YACE,MAAM,IAAIJ,MAAM;IACpB;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C"}
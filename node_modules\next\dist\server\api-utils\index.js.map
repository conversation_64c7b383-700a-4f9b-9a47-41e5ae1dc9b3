{"version": 3, "sources": ["../../../src/server/api-utils/index.ts"], "names": ["sendStatusCode", "redirect", "checkIsOnDemandRevalidate", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_PREVIEW_DATA", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "ApiError", "sendError", "setLazyProp", "res", "statusCode", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "req", "previewProps", "headers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "previewModeId", "get", "PRERENDER_REVALIDATE_HEADER", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "Symbol", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "constructor", "message", "statusMessage", "prop", "getter", "opts", "configurable", "optsReset", "writable", "set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAyBgBA,cAAc;eAAdA;;IAcAC,QAAQ;eAARA;;IAoBAC,yBAAyB;eAAzBA;;IAmBHC,4BAA4B;eAA5BA;;IACAC,0BAA0B;eAA1BA;;IAEAC,sBAAsB;eAAtBA;;IAEAC,mBAAmB;eAAnBA;;IACAC,sBAAsB;eAAtBA;;IAEGC,gBAAgB;eAAhBA;;IAyDHC,QAAQ;eAARA;;IAeGC,SAAS;eAATA;;IAoBAC,WAAW;eAAXA;;;yBA7Ke;2BAIxB;AAgBA,SAASX,eACdY,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASX,SACdW,GAAoB,EACpBE,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,IAAIC,MACR,CAAC,qKAAqK,CAAC;IAE3K;IACAJ,IAAIK,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CH,IAAIO,KAAK,CAACJ;IACVH,IAAIQ,GAAG;IACP,OAAOR;AACT;AAEO,SAASV,0BACdmB,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,UAAUC,uBAAc,CAACC,IAAI,CAACJ,IAAIE,OAAO;IAE/C,MAAMG,gBAAgBH,QAAQI,GAAG,CAACC,sCAA2B;IAC7D,MAAMC,uBAAuBH,kBAAkBJ,aAAaI,aAAa;IAEzE,MAAMI,0BAA0BP,QAAQQ,GAAG,CACzCC,qDAA0C;IAG5C,OAAO;QAAEH;QAAsBC;IAAwB;AACzD;AAEO,MAAM3B,+BAA+B,CAAC,kBAAkB,CAAC;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC;AAExD,MAAMC,yBAAyB,IAAI,OAAO;AAE1C,MAAMC,sBAAsB2B,OAAO7B;AACnC,MAAMG,yBAAyB0B,OAAO9B;AAEtC,SAASK,iBACdI,GAAuB,EACvBsB,UAEI,CAAC,CAAC;IAEN,IAAI3B,0BAA0BK,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAEuB,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAWzB,IAAI0B,SAAS,CAAC;IAC/B1B,IAAI2B,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACdA,WACA,EAAE;QACNF,UAAUhC,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEuC,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAU/B,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEsC,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,SAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,KAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAACzC,KAAKL,wBAAwB;QACjD+C,OAAO;QACPC,YAAY;IACd;IACA,OAAO3C;AACT;AAKO,MAAMH,iBAAiBO;IAG5BwC,YAAY3C,UAAkB,EAAE4C,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAC5C,UAAU,GAAGA;IACpB;AACF;AAQO,SAASH,UACdE,GAAoB,EACpBC,UAAkB,EAClB4C,OAAe;IAEf7C,IAAIC,UAAU,GAAGA;IACjBD,IAAI8C,aAAa,GAAGD;IACpB7C,IAAIQ,GAAG,CAACqC;AACV;AAYO,SAAS9C,YACd,EAAEU,GAAG,EAAa,EAClBsC,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMP,YAAY;IAAK;IACpD,MAAMQ,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5CZ,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;QAC/B,GAAGE,IAAI;QACPlC,KAAK;YACH,MAAM2B,QAAQM;YACd,8DAA8D;YAC9DR,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;YACvD,OAAOA;QACT;QACAW,KAAK,CAACX;YACJF,OAAOC,cAAc,CAAChC,KAAKsC,MAAM;gBAAE,GAAGI,SAAS;gBAAET;YAAM;QACzD;IACF;AACF"}
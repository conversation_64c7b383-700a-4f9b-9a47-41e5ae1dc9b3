{"version": 3, "sources": ["../../src/client/route-announcer.tsx"], "names": ["RouteAnnouncer", "nextjsRouteAnnouncerStyles", "border", "clip", "height", "margin", "overflow", "padding", "position", "top", "width", "whiteSpace", "wordWrap", "<PERSON><PERSON><PERSON>", "useRouter", "routeAnnouncement", "setRouteAnnouncement", "React", "useState", "previouslyLoaded<PERSON><PERSON>", "useRef", "useEffect", "current", "document", "title", "pageHeader", "querySelector", "content", "innerText", "textContent", "p", "aria-live", "id", "role", "style"], "mappings": ";;;;;;;;;;;;;;;IAmBaA,cAAc;eAAdA;;IA6Cb,OAA6B;eAA7B;;;;gEAhEkB;wBACQ;AAE1B,MAAMC,6BAAkD;IACtDC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,QAAQ;IACRC,UAAU;IACVC,SAAS;IACTC,UAAU;IACVC,KAAK;IACLC,OAAO;IAEP,wFAAwF;IACxFC,YAAY;IACZC,UAAU;AACZ;AAEO,MAAMZ,iBAAiB;IAC5B,MAAM,EAAEa,MAAM,EAAE,GAAGC,IAAAA,iBAAS;IAC5B,MAAM,CAACC,mBAAmBC,qBAAqB,GAAGC,cAAK,CAACC,QAAQ,CAAC;IAEjE,2EAA2E;IAC3E,qCAAqC;IACrC,MAAMC,uBAAuBF,cAAK,CAACG,MAAM,CAACP;IAE1C,4EAA4E;IAC5E,6EAA6E;IAC7E,8EAA8E;IAC9E,0EAA0E;IAC1E,iCAAiC;IACjC,mFAAmF;IACnFI,cAAK,CAACI,SAAS,CACb;QACE,4CAA4C;QAC5C,IAAIF,qBAAqBG,OAAO,KAAKT,QAAQ;QAC7CM,qBAAqBG,OAAO,GAAGT;QAE/B,IAAIU,SAASC,KAAK,EAAE;YAClBR,qBAAqBO,SAASC,KAAK;QACrC,OAAO;YACL,MAAMC,aAAaF,SAASG,aAAa,CAAC;gBAC1BD;YAAhB,MAAME,UAAUF,CAAAA,wBAAAA,8BAAAA,WAAYG,SAAS,YAArBH,wBAAyBA,8BAAAA,WAAYI,WAAW;YAEhEb,qBAAqBW,WAAWd;QAClC;IACF,GACA,wEAAwE;IACxE;QAACA;KAAO;IAGV,qBACE,6BAACiB;QACCC,aAAU,YAAY,qCAAqC;;QAC3DC,IAAG;QACHC,MAAK;QACLC,OAAOjC;OAENc;AAGP;MAEA,WAAef"}
{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/hydration-error-info.ts"], "names": ["hydrationErrorWarning", "hydrationErrorComponentStack", "patchConsoleError", "knownHydrationWarnings", "Set", "prev", "console", "error", "msg", "serverContent", "clientContent", "componentStack", "has", "replace", "apply", "arguments"], "mappings": ";;;;;;;;;;;;;;;;IAAWA,qBAAqB;eAArBA;;IACAC,4BAA4B;eAA5BA;;IAiBKC,iBAAiB;eAAjBA;;;AAlBT,IAAIF;AACJ,IAAIC;AAEX,iIAAiI;AACjI,MAAME,yBAAyB,IAAIC,IAAI;IACrC;IACA;IACA;IACA;IACA;CACD;AAQM,SAASF;IACd,MAAMG,OAAOC,QAAQC,KAAK;IAC1BD,QAAQC,KAAK,GAAG,SAAUC,GAAG,EAAEC,aAAa,EAAEC,aAAa,EAAEC,cAAc;QACzE,IAAIR,uBAAuBS,GAAG,CAACJ,MAAM;YACnCR,wBAAwBQ,IACrBK,OAAO,CAAC,MAAMJ,eACdI,OAAO,CAAC,MAAMH,eACdG,OAAO,CAAC,MAAM;YACjBZ,+BAA+BU;QACjC;QAEA,uCAAuC;QACvCN,KAAKS,KAAK,CAACR,SAASS;IACtB;AACF"}
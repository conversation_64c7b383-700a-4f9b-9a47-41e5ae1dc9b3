import Link from 'next/link'
import { Plane, Phone, Mail, MapPin, Facebook, Instagram, Twitter } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Plane className="h-8 w-8 text-primary-400" />
              <span className="text-2xl font-bold arabic-display">رحلاتي</span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              منصة شاملة لحجز الرحلات والفنادق وتأجير السيارات. نوفر لك أفضل الأسعار وأجود الخدمات لرحلة مميزة.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-primary-400 transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">روابط سريعة</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/flights" className="text-gray-300 hover:text-primary-400 transition-colors">
                  حجز الطيران
                </Link>
              </li>
              <li>
                <Link href="/hotels" className="text-gray-300 hover:text-primary-400 transition-colors">
                  حجز الفنادق
                </Link>
              </li>
              <li>
                <Link href="/cars" className="text-gray-300 hover:text-primary-400 transition-colors">
                  تأجير السيارات
                </Link>
              </li>
              <li>
                <Link href="/packages" className="text-gray-300 hover:text-primary-400 transition-colors">
                  الباقات السياحية
                </Link>
              </li>
              <li>
                <Link href="/destinations" className="text-gray-300 hover:text-primary-400 transition-colors">
                  الوجهات السياحية
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">الدعم والمساعدة</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-300 hover:text-primary-400 transition-colors">
                  من نحن
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-primary-400 transition-colors">
                  اتصل بنا
                </Link>
              </li>
              <li>
                <Link href="/help" className="text-gray-300 hover:text-primary-400 transition-colors">
                  مركز المساعدة
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-gray-300 hover:text-primary-400 transition-colors">
                  الشروط والأحكام
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="text-gray-300 hover:text-primary-400 transition-colors">
                  سياسة الخصوصية
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <Phone className="h-5 w-5 text-primary-400" />
                <span className="text-gray-300">+218-92-035-7271</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="h-5 w-5 text-primary-400" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <MapPin className="h-5 w-5 text-primary-400" />
                <span className="text-gray-300">طرابلس، ليبيا</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 رحلاتي. جميع الحقوق محفوظة.
          </p>
        </div>
      </div>
    </footer>
  )
}

{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/next-fixture.ts"], "names": ["applyNextFixture", "NextFixtureImpl", "constructor", "testInfo", "options", "worker", "page", "fetchHandlers", "testId", "testHeaders", "String", "proxyPort", "handleFetch", "bind", "onFetch", "route", "handleRoute", "teardown", "cleanupTest", "handler", "push", "request", "reportFetch", "req", "slice", "reverse", "result", "clone", "fetch<PERSON><PERSON><PERSON>", "fetch", "undefined", "use", "nextOptions", "nextWorker", "fixture"], "mappings": ";;;;+BAy<PERSON><PERSON>;;;eAAAA;;;2BArDM;wBACA;AAM5B,MAAMC;IAIJC,YACUC,UACAC,SACAC,QACAC,KACR;wBAJQH;uBACAC;sBACAC;oBACAC;aANFC,gBAAgC,EAAE;QAQxC,IAAI,CAACC,MAAM,GAAGL,SAASK,MAAM;QAC7B,MAAMC,cAAc;YAClB,wBAAwBC,OAAOL,OAAOM,SAAS;YAC/C,kBAAkB,IAAI,CAACH,MAAM;QAC/B;QACA,MAAMI,cAAc,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,IAAI;QAC9CR,OAAOS,OAAO,CAAC,IAAI,CAACN,MAAM,EAAEI;QAC5B,IAAI,CAACN,IAAI,CAACS,KAAK,CAAC,MAAM,CAACA,QACrBC,IAAAA,sBAAW,EAACD,OAAOT,MAAMG,aAAaG;IAE1C;IAEAK,WAAiB;QACf,IAAI,CAACZ,MAAM,CAACa,WAAW,CAAC,IAAI,CAACV,MAAM;IACrC;IAEAM,QAAQK,OAAqB,EAAQ;QACnC,IAAI,CAACZ,aAAa,CAACa,IAAI,CAACD;IAC1B;IAEA,MAAcP,YAAYS,OAAgB,EAA+B;QACvE,OAAOC,IAAAA,mBAAW,EAAC,IAAI,CAACnB,QAAQ,EAAEkB,SAAS,OAAOE;YAChD,KAAK,MAAMJ,WAAW,IAAI,CAACZ,aAAa,CAACiB,KAAK,GAAGC,OAAO,GAAI;gBAC1D,MAAMC,SAAS,MAAMP,QAAQI,IAAII,KAAK;gBACtC,IAAID,QAAQ;oBACV,OAAOA;gBACT;YACF;YACA,IAAI,IAAI,CAACtB,OAAO,CAACwB,aAAa,EAAE;gBAC9B,OAAOC,MAAMN,IAAII,KAAK;YACxB;YACA,OAAOG;QACT;IACF;AACF;AAEO,eAAe9B,iBACpB+B,GAA4C,EAC5C,EACE5B,QAAQ,EACR6B,WAAW,EACXC,UAAU,EACV3B,IAAI,EAML;IAED,MAAM4B,UAAU,IAAIjC,gBAAgBE,UAAU6B,aAAaC,YAAY3B;IAEvE,MAAMyB,IAAIG;IAEVA,QAAQjB,QAAQ;AAClB"}
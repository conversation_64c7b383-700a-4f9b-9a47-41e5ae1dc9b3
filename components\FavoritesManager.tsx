'use client'

import { useState, useEffect } from 'react'
import { Heart, X, Plane, MapPin, Calendar, Star, Filter } from 'lucide-react'

interface FavoriteTrip {
  id: string
  type: 'flight' | 'hotel' | 'package'
  title: string
  destination: string
  price: number
  currency: string
  image: string
  rating: number
  description: string
  dateAdded: string
  details: {
    duration?: string
    departure?: string
    arrival?: string
    checkIn?: string
    checkOut?: string
    includes?: string[]
  }
}

interface FavoritesManagerProps {
  isOpen: boolean
  onClose: () => void
}

export default function FavoritesManager({ isOpen, onClose }: FavoritesManagerProps) {
  const [favorites, setFavorites] = useState<FavoriteTrip[]>([])
  const [filter, setFilter] = useState<'all' | 'flight' | 'hotel' | 'package'>('all')
  const [sortBy, setSortBy] = useState<'date' | 'price' | 'rating'>('date')

  // Load favorites from localStorage on component mount
  useEffect(() => {
    const savedFavorites = localStorage.getItem('userFavorites')
    if (savedFavorites) {
      setFavorites(JSON.parse(savedFavorites))
    } else {
      // Add some sample favorites for demonstration
      const sampleFavorites: FavoriteTrip[] = [
        {
          id: '1',
          type: 'flight',
          title: 'رحلة إلى دبي',
          destination: 'دبي، الإمارات',
          price: 1250,
          currency: 'ريال',
          image: 'https://images.unsplash.com/photo-1512453979798-5ea266f8880c?w=300&h=200&fit=crop',
          rating: 4.8,
          description: 'رحلة مباشرة مع الخطوط السعودية',
          dateAdded: '2024-01-15',
          details: {
            duration: '3 ساعات 45 دقيقة',
            departure: 'الرياض (RUH)',
            arrival: 'دبي (DXB)'
          }
        },
        {
          id: '2',
          type: 'hotel',
          title: 'فندق برج العرب',
          destination: 'دبي، الإمارات',
          price: 2500,
          currency: 'ريال',
          image: 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=300&h=200&fit=crop',
          rating: 5.0,
          description: 'فندق فاخر 7 نجوم مع إطلالة على البحر',
          dateAdded: '2024-01-14',
          details: {
            checkIn: '2024-02-15',
            checkOut: '2024-02-20',
            includes: ['إفطار مجاني', 'واي فاي مجاني', 'مسبح', 'سبا']
          }
        },
        {
          id: '3',
          type: 'package',
          title: 'باقة إسطنبول الشاملة',
          destination: 'إسطنبول، تركيا',
          price: 3200,
          currency: 'ريال',
          image: 'https://images.unsplash.com/photo-1541432901042-2d8bd64b4a9b?w=300&h=200&fit=crop',
          rating: 4.7,
          description: 'باقة شاملة لمدة 5 أيام تشمل الطيران والإقامة والجولات',
          dateAdded: '2024-01-13',
          details: {
            duration: '5 أيام / 4 ليالي',
            includes: ['طيران ذهاب وإياب', 'إقامة 4 نجوم', 'جولات سياحية', 'وجبات']
          }
        }
      ]
      setFavorites(sampleFavorites)
      localStorage.setItem('userFavorites', JSON.stringify(sampleFavorites))
    }
  }, [])

  // Save favorites to localStorage whenever favorites change
  useEffect(() => {
    localStorage.setItem('userFavorites', JSON.stringify(favorites))
  }, [favorites])

  const removeFavorite = (id: string) => {
    setFavorites(favorites.filter(fav => fav.id !== id))
  }

  const filteredFavorites = favorites.filter(fav =>
    filter === 'all' || fav.type === filter
  )

  const sortedFavorites = [...filteredFavorites].sort((a, b) => {
    switch (sortBy) {
      case 'price':
        return a.price - b.price
      case 'rating':
        return b.rating - a.rating
      case 'date':
      default:
        return new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime()
    }
  })

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'flight':
        return <Plane className="h-4 w-4" />
      case 'hotel':
        return <MapPin className="h-4 w-4" />
      case 'package':
        return <Star className="h-4 w-4" />
      default:
        return <Heart className="h-4 w-4" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'flight':
        return 'طيران'
      case 'hotel':
        return 'فندق'
      case 'package':
        return 'باقة'
      default:
        return 'مفضلة'
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3 space-x-reverse">
              <Heart className="h-6 w-6 text-red-300" />
              <h2 className="text-2xl font-bold arabic-title">رحلاتي المفضلة</h2>
              <span className="bg-white/20 px-3 py-1 rounded-full text-sm">
                {favorites.length} رحلة
              </span>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Filters and Sort */}
        <div className="p-6 bg-gradient-to-r from-slate-50 to-blue-50 border-b border-blue-200">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <Filter className="h-5 w-5 text-blue-600" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="bg-white border border-blue-300 rounded-xl px-4 py-3 arabic-text shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
              >
                <option value="all" className="arabic-text">جميع الرحلات</option>
                <option value="flight" className="arabic-text">الطيران</option>
                <option value="hotel" className="arabic-text">الفنادق</option>
                <option value="package" className="arabic-text">الباقات</option>
              </select>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-blue-700 font-medium arabic-text">ترتيب حسب:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="bg-white border border-blue-300 rounded-xl px-4 py-3 arabic-text shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
              >
                <option value="date" className="arabic-text">تاريخ الإضافة</option>
                <option value="price" className="arabic-text">السعر</option>
                <option value="rating" className="arabic-text">التقييم</option>
              </select>
            </div>
          </div>
        </div>

        {/* Favorites List */}
        <div className="p-6 max-h-96 overflow-y-auto">
          {sortedFavorites.length === 0 ? (
            <div className="text-center py-12">
              <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2 arabic-title">
                لا توجد رحلات مفضلة
              </h3>
              <p className="text-gray-500 arabic-text">
                ابدأ بإضافة رحلاتك المفضلة لتظهر هنا
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {sortedFavorites.map((favorite) => (
                <div
                  key={favorite.id}
                  className="bg-gray-50 rounded-2xl overflow-hidden hover:shadow-lg transition-shadow group"
                >
                  {/* Image */}
                  <div className="relative h-40">
                    <img
                      src={favorite.image}
                      alt={favorite.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-3 right-3 flex items-center space-x-2 space-x-reverse">
                      <span className="bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 space-x-reverse">
                        {getTypeIcon(favorite.type)}
                        <span>{getTypeLabel(favorite.type)}</span>
                      </span>
                    </div>
                    <button
                      onClick={() => removeFavorite(favorite.id)}
                      className="absolute top-3 left-3 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-bold text-gray-800 arabic-title">{favorite.title}</h3>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        <span className="text-sm font-medium">{favorite.rating}</span>
                      </div>
                    </div>

                    <p className="text-gray-600 text-sm mb-2 arabic-text">{favorite.destination}</p>
                    <p className="text-gray-500 text-sm mb-3 arabic-text">{favorite.description}</p>

                    <div className="flex items-center justify-between">
                      <div className="text-right">
                        <span className="text-2xl font-bold text-blue-600">{favorite.price}</span>
                        <span className="text-gray-500 text-sm mr-1">{favorite.currency}</span>
                      </div>
                      <button className="btn-primary text-sm px-4 py-2">
                        احجز الآن
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

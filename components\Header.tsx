'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X, Plane, User, Search } from 'lucide-react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <header className={`sticky top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100'
        : 'bg-white shadow-md'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-18">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 space-x-reverse group">
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <Plane className="h-8 w-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent arabic-display">رحلاتي</span>
              <span className="text-xs text-gray-500 font-medium">منصة السفر الذكية</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الرئيسية
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/flights" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الطيران
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/hotels" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الفنادق
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/cars" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              تأجير السيارات
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/packages" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الباقات السياحية
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/destinations" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الوجهات
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
          </nav>

          {/* User Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <Link href="/auth/login" className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 rounded-xl hover:bg-blue-50 group">
              <User className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-medium">تسجيل الدخول</span>
            </Link>
            <Link href="/auth/register" className="btn-primary relative overflow-hidden">
              <span className="relative z-10">إنشاء حساب</span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden p-3 rounded-xl hover:bg-blue-50 transition-all duration-300 group relative"
          >
            <div className="relative">
              {isMenuOpen ? (
                <X className="h-6 w-6 text-gray-700 group-hover:text-blue-600 transition-colors" />
              ) : (
                <Menu className="h-6 w-6 text-gray-700 group-hover:text-blue-600 transition-colors" />
              )}
            </div>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <Link href="/" className="text-gray-700 hover:text-primary-600 transition-colors">
                الرئيسية
              </Link>
              <Link href="/flights" className="text-gray-700 hover:text-primary-600 transition-colors">
                الطيران
              </Link>
              <Link href="/hotels" className="text-gray-700 hover:text-primary-600 transition-colors">
                الفنادق
              </Link>
              <Link href="/cars" className="text-gray-700 hover:text-primary-600 transition-colors">
                تأجير السيارات
              </Link>
              <Link href="/packages" className="text-gray-700 hover:text-primary-600 transition-colors">
                الباقات السياحية
              </Link>
              <Link href="/destinations" className="text-gray-700 hover:text-primary-600 transition-colors">
                الوجهات
              </Link>
              <div className="pt-4 border-t border-gray-200">
                <Link href="/auth/login" className="block text-gray-700 hover:text-primary-600 transition-colors mb-2">
                  تسجيل الدخول
                </Link>
                <Link href="/auth/register" className="btn-primary inline-block">
                  إنشاء حساب
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

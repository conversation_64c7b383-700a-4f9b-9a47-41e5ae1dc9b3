'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X, Plane, User, Search, Heart } from 'lucide-react'
import FavoritesManager from './FavoritesManager'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [showFavorites, setShowFavorites] = useState(false)
  const [favoritesCount, setFavoritesCount] = useState(0)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Update favorites count
  useEffect(() => {
    const updateFavoritesCount = () => {
      const savedFavorites = localStorage.getItem('userFavorites')
      if (savedFavorites) {
        const favorites = JSON.parse(savedFavorites)
        setFavoritesCount(favorites.length)
      }
    }

    updateFavoritesCount()

    // Listen for storage changes
    window.addEventListener('storage', updateFavoritesCount)

    // Listen for custom events when favorites are updated
    window.addEventListener('favoritesUpdated', updateFavoritesCount)

    return () => {
      window.removeEventListener('storage', updateFavoritesCount)
      window.removeEventListener('favoritesUpdated', updateFavoritesCount)
    }
  }, [])

  return (
    <header className={`sticky top-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-lg shadow-lg border-b border-gray-100'
        : 'bg-white shadow-md'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-18">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 space-x-reverse group">
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-2 rounded-xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <Plane className="h-8 w-8 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-black bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent arabic-title">رحلاتي</span>
              <span className="text-xs text-gray-500 font-medium arabic-text">منصة السفر الذكية</span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الرئيسية
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/flights" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الطيران
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/hotels" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الفنادق
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/cars" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              تأجير السيارات
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/packages" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الباقات السياحية
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
            <Link href="/destinations" className="relative group px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 font-medium">
              الوجهات
              <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 group-hover:w-full transition-all duration-300"></span>
            </Link>
          </nav>

          {/* User Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            {/* Favorites Button */}
            <button
              onClick={() => setShowFavorites(true)}
              className="relative flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-700 hover:text-red-600 transition-all duration-300 rounded-xl hover:bg-red-50 group"
            >
              <Heart className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-medium arabic-text">المفضلة</span>
              {favoritesCount > 0 && (
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                  {favoritesCount}
                </span>
              )}
            </button>

            <Link href="/auth/login" className="flex items-center space-x-2 space-x-reverse px-4 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300 rounded-xl hover:bg-blue-50 group">
              <User className="h-5 w-5 group-hover:scale-110 transition-transform" />
              <span className="font-medium">تسجيل الدخول</span>
            </Link>
            <Link href="/auth/register" className="btn-primary relative overflow-hidden">
              <span className="relative z-10">إنشاء حساب</span>
              <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="lg:hidden p-3 rounded-xl hover:bg-blue-50 transition-all duration-300 group relative"
          >
            <div className="relative">
              {isMenuOpen ? (
                <X className="h-6 w-6 text-gray-700 group-hover:text-blue-600 transition-colors" />
              ) : (
                <Menu className="h-6 w-6 text-gray-700 group-hover:text-blue-600 transition-colors" />
              )}
            </div>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-6 bg-gradient-to-br from-slate-800 to-blue-900 border-t border-blue-700/30 rounded-b-2xl shadow-2xl">
            <nav className="flex flex-col space-y-4 px-4">
              <Link href="/" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                الرئيسية
              </Link>
              <Link href="/flights" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                الطيران
              </Link>
              <Link href="/hotels" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                الفنادق
              </Link>
              <Link href="/cars" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                تأجير السيارات
              </Link>
              <Link href="/packages" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                الباقات السياحية
              </Link>
              <Link href="/destinations" className="text-white hover:text-blue-300 transition-colors py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                الوجهات
              </Link>
              <div className="pt-4 border-t border-blue-700/30 mt-4">
                <Link href="/auth/login" className="block text-white hover:text-blue-300 transition-colors mb-3 py-2 px-4 rounded-lg hover:bg-white/10 arabic-text">
                  تسجيل الدخول
                </Link>
                <Link href="/auth/register" className="btn-primary inline-block w-full text-center">
                  إنشاء حساب
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>

      {/* Favorites Manager Modal */}
      <FavoritesManager
        isOpen={showFavorites}
        onClose={() => setShowFavorites(false)}
      />
    </header>
  )
}

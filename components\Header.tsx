'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Menu, X, Plane, User, Search } from 'lucide-react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 space-x-reverse">
            <Plane className="h-8 w-8 text-primary-600" />
            <span className="text-2xl font-bold text-primary-600 arabic-display">رحلاتي</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8 space-x-reverse">
            <Link href="/" className="text-gray-700 hover:text-primary-600 transition-colors">
              الرئيسية
            </Link>
            <Link href="/flights" className="text-gray-700 hover:text-primary-600 transition-colors">
              الطيران
            </Link>
            <Link href="/hotels" className="text-gray-700 hover:text-primary-600 transition-colors">
              الفنادق
            </Link>
            <Link href="/cars" className="text-gray-700 hover:text-primary-600 transition-colors">
              تأجير السيارات
            </Link>
            <Link href="/packages" className="text-gray-700 hover:text-primary-600 transition-colors">
              الباقات السياحية
            </Link>
            <Link href="/destinations" className="text-gray-700 hover:text-primary-600 transition-colors">
              الوجهات
            </Link>
          </nav>

          {/* User Actions */}
          <div className="hidden md:flex items-center space-x-4 space-x-reverse">
            <Link href="/auth/login" className="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-primary-600 transition-colors">
              <User className="h-5 w-5" />
              <span>تسجيل الدخول</span>
            </Link>
            <Link href="/auth/register" className="btn-primary">
              إنشاء حساب
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-200">
            <nav className="flex flex-col space-y-4">
              <Link href="/" className="text-gray-700 hover:text-primary-600 transition-colors">
                الرئيسية
              </Link>
              <Link href="/flights" className="text-gray-700 hover:text-primary-600 transition-colors">
                الطيران
              </Link>
              <Link href="/hotels" className="text-gray-700 hover:text-primary-600 transition-colors">
                الفنادق
              </Link>
              <Link href="/cars" className="text-gray-700 hover:text-primary-600 transition-colors">
                تأجير السيارات
              </Link>
              <Link href="/packages" className="text-gray-700 hover:text-primary-600 transition-colors">
                الباقات السياحية
              </Link>
              <Link href="/destinations" className="text-gray-700 hover:text-primary-600 transition-colors">
                الوجهات
              </Link>
              <div className="pt-4 border-t border-gray-200">
                <Link href="/auth/login" className="block text-gray-700 hover:text-primary-600 transition-colors mb-2">
                  تسجيل الدخول
                </Link>
                <Link href="/auth/register" className="btn-primary inline-block">
                  إنشاء حساب
                </Link>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

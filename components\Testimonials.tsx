'use client'

import { useState, useEffect } from 'react'
import { Star, Quote, ChevronLeft, ChevronRight } from 'lucide-react'

const testimonials = [
  {
    id: 1,
    name: 'أحمد محمد',
    location: 'الرياض، السعودية',
    rating: 5,
    text: 'تجربة رائعة مع رحلاتي! حجزت رحلة إلى دبي والخدمة كانت ممتازة من البداية للنهاية. الأسعار تنافسية والدعم الفني متاح دائماً.',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
    trip: 'رحلة إلى دبي'
  },
  {
    id: 2,
    name: 'فاطمة العلي',
    location: 'الكويت',
    rating: 5,
    text: 'استخدمت الموقع لحجز فندق في إسطنبول وكانت التجربة سهلة وسريعة. الفندق كان بالضبط كما هو موضح والسعر كان أفضل من المواقع الأخرى.',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
    trip: 'إقامة في إسطنبول'
  },
  {
    id: 3,
    name: 'محمد الزهراني',
    location: 'جدة، السعودية',
    rating: 5,
    text: 'حجزت رحلة عائلية إلى القاهرة عبر رحلاتي. كل شيء كان منظم بشكل مثالي، من تذاكر الطيران إلى الفندق. أنصح الجميع بالتجربة.',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
    trip: 'رحلة عائلية إلى القاهرة'
  },
  {
    id: 4,
    name: 'نورا السالم',
    location: 'الدوحة، قطر',
    rating: 5,
    text: 'خدمة عملاء ممتازة! واجهت مشكلة في الحجز وتم حلها فوراً. الموقع سهل الاستخدام والأسعار شفافة بدون رسوم خفية.',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
    trip: 'رحلة إلى لندن'
  },
  {
    id: 5,
    name: 'عبدالله المطيري',
    location: 'الكويت',
    rating: 5,
    text: 'أفضل موقع لحجز الرحلات! استخدمته عدة مرات وفي كل مرة أحصل على أفضل الأسعار. التطبيق سريع والدفع آمن.',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face',
    trip: 'رحلات متعددة'
  }
]

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section className="py-20 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"4\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
      <div className="absolute top-0 left-0 w-96 h-96 bg-blue-500/10 rounded-full -translate-x-48 -translate-y-48 animate-pulse"></div>
      <div className="absolute bottom-0 right-0 w-64 h-64 bg-purple-500/10 rounded-full translate-x-32 translate-y-32 animate-float"></div>

      <div className="container mx-auto px-4 relative">
        {/* Section Header */}
        <div className="text-center mb-16 animate-fade-in">
          <span className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-4">
            آراء عملائنا
          </span>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 arabic-display">
            ماذا يقول عملاؤنا؟
          </h2>
          <p className="text-blue-100 text-xl max-w-3xl mx-auto leading-relaxed">
            اكتشف تجارب عملائنا الحقيقية ولماذا يثقون في رحلاتي لجميع احتياجاتهم السياحية
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div className="relative max-w-4xl mx-auto">
          <div className="overflow-hidden rounded-3xl">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(${currentIndex * -100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                  <div className="bg-white/10 backdrop-blur-lg rounded-3xl p-8 md:p-12 border border-white/20 relative">
                    {/* Quote Icon */}
                    <div className="absolute top-6 right-6 w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                      <Quote className="h-6 w-6 text-white" />
                    </div>

                    <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8 md:space-x-reverse">
                      {/* User Image */}
                      <div className="relative">
                        <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white/20">
                          <img 
                            src={testimonial.image} 
                            alt={testimonial.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs font-bold">✓</span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 text-center md:text-right">
                        {/* Rating */}
                        <div className="flex justify-center md:justify-end space-x-1 space-x-reverse mb-4">
                          {[...Array(testimonial.rating)].map((_, i) => (
                            <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                          ))}
                        </div>

                        {/* Testimonial Text */}
                        <p className="text-white text-lg md:text-xl leading-relaxed mb-6 italic">
                          "{testimonial.text}"
                        </p>

                        {/* User Info */}
                        <div className="space-y-2">
                          <h4 className="text-xl font-bold text-white">{testimonial.name}</h4>
                          <p className="text-blue-200">{testimonial.location}</p>
                          <div className="inline-block px-3 py-1 bg-white/20 rounded-full text-sm text-blue-100">
                            {testimonial.trip}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Buttons */}
          <button
            onClick={prevTestimonial}
            className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors group"
          >
            <ChevronLeft className="h-6 w-6 group-hover:scale-110 transition-transform" />
          </button>
          <button
            onClick={nextTestimonial}
            className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors group"
          >
            <ChevronRight className="h-6 w-6 group-hover:scale-110 transition-transform" />
          </button>
        </div>

        {/* Dots Indicator */}
        <div className="flex justify-center space-x-2 space-x-reverse mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-white scale-125' 
                  : 'bg-white/40 hover:bg-white/60'
              }`}
            />
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 animate-scale-in">
          <div className="text-center">
            <div className="text-4xl md:text-5xl font-bold text-white mb-2">4.9</div>
            <div className="text-blue-200 font-medium">تقييم العملاء</div>
          </div>
          <div className="text-center">
            <div className="text-4xl md:text-5xl font-bold text-white mb-2">98%</div>
            <div className="text-blue-200 font-medium">رضا العملاء</div>
          </div>
          <div className="text-center">
            <div className="text-4xl md:text-5xl font-bold text-white mb-2">15K+</div>
            <div className="text-blue-200 font-medium">تقييم إيجابي</div>
          </div>
          <div className="text-center">
            <div className="text-4xl md:text-5xl font-bold text-white mb-2">24/7</div>
            <div className="text-blue-200 font-medium">دعم العملاء</div>
          </div>
        </div>
      </div>
    </section>
  )
}
